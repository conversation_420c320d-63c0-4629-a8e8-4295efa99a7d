const fs = require('fs');
const fsPromises = require('fs').promises;
const path = require('path');

// Use real UUID - no more mocks
const { v4: uuidv4 } = require('uuid');

const PROFILES_BASE_DIR = path.join(__dirname, '..', '..', 'profiles');
const PROFILE_COOLDOWN_MS = 15 * 60 * 1000; // 15 minutes in milliseconds
const CHROME_PROFILE_MARKERS = ['Default', 'First Run', 'Local State', 'Login Data']; // Files/folders that indicate a Chrome profile

class ProfileManager {
    constructor(dbManager) {
        this.dbManager = dbManager;
        this.activeProfiles = new Set(); // Track profiles currently in use
        this._ensureProfilesBaseDir();
        console.log('✅ Profile manager initialized');
    }

    /**
     * Generate unique ID
     * @returns {string} Unique ID
     */
    generateId() {
        return uuidv4();
    }

    _ensureProfilesBaseDir() {
        if (!fs.existsSync(PROFILES_BASE_DIR)) {
            fs.mkdirSync(PROFILES_BASE_DIR, { recursive: true });
            console.log(`📁 Created profiles base directory: ${PROFILES_BASE_DIR}`);
        }
    }

    /**
     * Creates a new profile or updates an existing one.
     * Also ensures the userDataDir exists.
     */
    saveProfile(profileData) {
        if (!profileData || !profileData.id) {
            console.error('Profile ID is required to save a profile.', { profileData });
            return null;
        }

        let { userDataDir } = profileData;
        if (!userDataDir) {
            userDataDir = path.join(PROFILES_BASE_DIR, profileData.id);
            profileData.userDataDir = userDataDir;
        }

        // Ensure the physical directory for userDataDir exists
        if (!fs.existsSync(userDataDir)) {
            try {
                fs.mkdirSync(userDataDir, { recursive: true });
                console.log(`📁 Created userDataDir for profile ${profileData.id}: ${userDataDir}`);
            } catch (error) {
                console.error(`❌ Failed to create userDataDir for profile ${profileData.id}:`, error);
                return null;
            }
        }

        // Default values for a new profile
        const defaults = {
            name: profileData.id,
            email: '',
            password: '',
            isValid: true,
            lastUsed: 0,
            cooldownExpiresAt: 0,
            proxyId: null,
            notes: '',
            type: 'standard',
            isGoogleLoggedIn: false,
            googleEmail: ''
        };

        // Prepare data for saving
        const profileToSave = {
            id: profileData.id,
            name: profileData.name || defaults.name,
            email: profileData.email || defaults.email,
            password: profileData.password || defaults.password,
            userDataDir: userDataDir,
            isValid: profileData.isValid !== undefined ? profileData.isValid : defaults.isValid,
            lastUsed: profileData.lastUsed || defaults.lastUsed,
            cooldownExpiresAt: profileData.cooldownExpiresAt || defaults.cooldownExpiresAt,
            proxyId: profileData.proxyId !== undefined ? profileData.proxyId : defaults.proxyId,
            notes: profileData.notes || defaults.notes,
            type: profileData.type || defaults.type,
            isGoogleLoggedIn: profileData.isGoogleLoggedIn || defaults.isGoogleLoggedIn,
            googleEmail: profileData.googleEmail || defaults.googleEmail,
            ...profileData
        };

        return this.dbManager.saveProfile(profileToSave);
    }

    getAllProfiles() {
        return this.dbManager.getAllProfiles();
    }

    getProfile(profileId) {
        return this.dbManager.getProfile(profileId);
    }

    updateProfile(profileId, profileData) {
        return this.dbManager.updateProfile(profileId, profileData);
    }

    deleteProfile(profileId) {
        const profile = this.dbManager.getProfile(profileId);
        if (!profile) {
            console.error(`❌ Profile with id "${profileId}" not found for deletion.`);
            return false;
        }

        // Delete userDataDir if it exists
        if (profile.userDataDir && fs.existsSync(profile.userDataDir)) {
            try {
                fs.rmSync(profile.userDataDir, { recursive: true, force: true });
                console.log(`📁 Deleted userDataDir for profile ${profileId}: ${profile.userDataDir}`);
            } catch (error) {
                console.error(`❌ Failed to delete userDataDir for profile ${profileId}:`, error);
            }
        }

        return this.dbManager.deleteProfile(profileId);
    }

    createProfile(profileData) {
        const profileId = uuidv4();
        const profileWithId = { ...profileData, id: profileId };
        return this.saveProfile(profileWithId);
    }

    async getAvailableProfiles(excludeCooldown = true) {
        try {
            const allProfiles = await this.dbManager.getAllProfiles();
            const now = Date.now();

            return allProfiles.filter(profile => {
                // Ensure profile exists and has required fields
                if (!profile || !profile.id) {
                    return false;
                }

                // Check if profile is currently in use
                if (this.activeProfiles.has(profile.id)) {
                    return false;
                }

                // Check cooldown if enabled
                if (excludeCooldown && profile.cooldownExpiresAt && profile.cooldownExpiresAt > now) {
                    return false;
                }

                return true;
            }).sort((a, b) => {
                // Sort by lastUsed (oldest first)
                return (a.lastUsed || 0) - (b.lastUsed || 0);
            });
        } catch (error) {
            console.error('Failed to get available profiles:', error);
            throw error;
        }
    }

    async markProfileAsUsed(profileId, cooldownMinutes = 60) {
        try {
            const now = Date.now();
            const cooldownExpiresAt = now + (cooldownMinutes * 60 * 1000);

            await this.dbManager.updateProfile(profileId, {
                lastUsed: now,
                cooldownExpiresAt: cooldownExpiresAt
            });

            console.log(`Profile ${profileId} marked as used with ${cooldownMinutes}min cooldown`);
        } catch (error) {
            console.error('Failed to mark profile as used:', error);
            throw error;
        }
    }

    async validateProfile(profileId) {
        try {
            const profile = this.dbManager.getProfile(profileId);
            if (!profile) {
                return { valid: false, error: 'Profile not found' };
            }

            // Check if userDataDir exists
            if (profile.userDataDir) {
                if (!fs.existsSync(profile.userDataDir)) {
                    return { valid: false, error: 'Profile directory not found' };
                }
            }

            // For Chrome profiles, we don't require email/password
            // They will login manually in the browser
            if (profile.type === 'chrome') {
                return { valid: true };
            }

            // For standard profiles, check required fields
            if (!profile.email || !profile.password) {
                return { valid: false, error: 'Missing email or password' };
            }

            return { valid: true };
        } catch (error) {
            console.error('Profile validation failed:', error);
            return { valid: false, error: error.message };
        }
    }

    async testProfile(profileId) {
        try {
            const profile = this.dbManager.getProfile(profileId);
            if (!profile) {
                throw new Error('Profile not found');
            }

            // Validate profile first
            const validation = await this.validateProfile(profileId);
            if (!validation.valid) {
                throw new Error(validation.error);
            }

            // For Chrome profiles, just check if directory exists and is accessible
            if (profile.type === 'chrome') {
                if (fs.existsSync(profile.userDataDir)) {
                    console.log(`✅ Chrome profile test successful: ${profile.name}`);
                    return { success: true, message: 'Chrome profile directory accessible' };
                } else {
                    throw new Error('Chrome profile directory not accessible');
                }
            }

            // For standard profiles, we would test Facebook login
            // But for now, just validate the profile structure
            console.log(`✅ Profile test successful: ${profile.name}`);
            return { success: true, message: 'Profile validation successful' };

        } catch (error) {
            console.error(`❌ Profile test failed: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    async importProfilesFromFacebot(accountsFilePath, commentsFilePath, sharesFilePath) {
        try {
            // Read accounts file (format: email:password)
            const accountsContent = await fsPromises.readFile(accountsFilePath, 'utf8');
            const accounts = accountsContent.split('\n')
                .filter(line => line.trim() && line.includes(':'))
                .map(line => {
                    const [email, password] = line.trim().split(':');
                    return { email, password };
                });

            // Read comments file (format: comment1 :: comment2 :: comment3)
            let comments = [];
            if (commentsFilePath) {
                const commentsContent = await fsPromises.readFile(commentsFilePath, 'utf8');
                comments = commentsContent.split('::')
                    .map(comment => comment.trim())
                    .filter(comment => comment);
            }

            // Read shares file (format: share1 :: share2 :: share3)
            let shares = [];
            if (sharesFilePath) {
                const sharesContent = await fsPromises.readFile(sharesFilePath, 'utf8');
                shares = sharesContent.split('::')
                    .map(share => share.trim())
                    .filter(share => share);
            }

            // Create profiles
            const importedProfiles = [];
            for (let i = 0; i < accounts.length; i++) {
                const account = accounts[i];
                const profileData = {
                    name: `Imported Profile ${i + 1}`,
                    email: account.email,
                    password: account.password,
                    facebook: {
                        comments: comments,
                        shares: shares,
                        settings: {
                            delayComment: 4,
                            delayShare: 7,
                            delayLogout: 3,
                            enableComments: true,
                            enableShares: true,
                            enableLikes: false,
                            enableDecoyLinks: false
                        }
                    }
                };

                const profile = await this.createProfile(profileData);
                importedProfiles.push(profile);
            }

            console.log(`Imported ${importedProfiles.length} profiles from facebot`);
            return { success: true, imported: importedProfiles.length, profiles: importedProfiles };
        } catch (error) {
            console.error('Failed to import profiles from facebot:', error);
            throw error;
        }
    }

    async exportProfiles(profileIds = null) {
        try {
            let profiles;
            if (profileIds) {
                profiles = await Promise.all(
                    profileIds.map(id => this.dbManager.getProfile(id))
                );
            } else {
                profiles = await this.dbManager.getAllProfiles();
            }

            // Remove sensitive data and format for export
            const exportData = profiles.map(profile => ({
                name: profile.name,
                email: profile.email,
                facebook: profile.facebook,
                isActive: profile.isActive,
                createdAt: profile.createdAt
            }));

            return {
                version: '1.0',
                exportedAt: Date.now(),
                profiles: exportData
            };
        } catch (error) {
            console.error('Failed to export profiles:', error);
            throw error;
        }
    }

    async importProfiles(exportData, includePasswords = false) {
        try {
            if (!exportData.profiles || !Array.isArray(exportData.profiles)) {
                throw new Error('Invalid export data format');
            }

            const importedProfiles = [];
            for (const profileData of exportData.profiles) {
                if (!includePasswords) {
                    // Skip profiles without passwords if not including them
                    if (!profileData.password) {
                        continue;
                    }
                }

                const profile = await this.createProfile(profileData);
                importedProfiles.push(profile);
            }

            console.log(`Imported ${importedProfiles.length} profiles`);
            return { success: true, imported: importedProfiles.length, profiles: importedProfiles };
        } catch (error) {
            console.error('Failed to import profiles:', error);
            throw error;
        }
    }

    // Profile rotation for automation
    async getNextProfile(excludeIds = []) {
        try {
            const availableProfiles = await this.getAvailableProfiles();
            const filteredProfiles = availableProfiles.filter(
                profile => !excludeIds.includes(profile.id)
            );

            if (filteredProfiles.length === 0) {
                return null;
            }

            // Return the profile that was used least recently
            return filteredProfiles[0];
        } catch (error) {
            console.error('Failed to get next profile:', error);
            throw error;
        }
    }

    // Mark profile as active (in use)
    markProfileActive(profileId) {
        this.activeProfiles.add(profileId);
    }

    // Mark profile as inactive (not in use)
    markProfileInactive(profileId) {
        this.activeProfiles.delete(profileId);
    }

    // Get profile usage statistics
    async getProfileStats(profileId) {
        try {
            const logs = await this.dbManager.getLogs({ profileId });

            const stats = {
                totalActions: logs.length,
                successfulActions: logs.filter(log => log.status === 'success').length,
                failedActions: logs.filter(log => log.status === 'failed').length,
                lastActivity: logs.length > 0 ? Math.max(...logs.map(log => log.timestamp)) : null,
                actionBreakdown: {}
            };

            // Count actions by type
            logs.forEach(log => {
                if (!stats.actionBreakdown[log.action]) {
                    stats.actionBreakdown[log.action] = { total: 0, success: 0, failed: 0 };
                }
                stats.actionBreakdown[log.action].total++;
                if (log.status === 'success') {
                    stats.actionBreakdown[log.action].success++;
                } else if (log.status === 'failed') {
                    stats.actionBreakdown[log.action].failed++;
                }
            });

            return stats;
        } catch (error) {
            console.error('Failed to get profile stats:', error);
            throw error;
        }
    }

    /**
     * Checks if a directory is a Chrome profile by looking for specific markers.
     * @param {string} dirPath - Path to the directory to check
     * @returns {boolean} True if the directory appears to be a Chrome profile
     */
    isChromeProfile(dirPath) {
        if (!fs.existsSync(dirPath)) return false;

        // Check for Chrome profile markers
        let markerCount = 0;
        for (const marker of CHROME_PROFILE_MARKERS) {
            if (fs.existsSync(path.join(dirPath, marker))) {
                markerCount++;
            }
        }

        // If at least 2 markers are found, consider it a Chrome profile
        return markerCount >= 2;
    }

    /**
     * Creates a new Chrome profile with the specified name.
     * @param {string} profileId - ID for the new profile
     * @param {string} profileName - Name for the new profile
     * @param {object} options - Additional options for the profile
     * @returns {object|null} The created profile object or null on failure
     */
    createChromeProfile(profileId, profileName, options = {}) {
        console.log(`Creating new Chrome profile: ${profileName} (${profileId})`);

        // Create the profile directory
        const userDataDir = path.join(PROFILES_BASE_DIR, profileId);
        if (!fs.existsSync(userDataDir)) {
            try {
                fs.mkdirSync(userDataDir, { recursive: true });
                console.log(`📁 Created directory for Chrome profile ${profileId}: ${userDataDir}`);
            } catch (error) {
                console.error(`❌ Failed to create directory for Chrome profile ${profileId}:`, error);
                return null;
            }
        }

        // Create the profile in the database
        const profileData = {
            id: profileId,
            name: profileName,
            userDataDir: userDataDir,
            type: 'chrome',
            isGoogleLoggedIn: false,
            googleEmail: '',
            ...options
        };

        return this.saveProfile(profileData);
    }

    /**
     * Updates the Google login status for a profile.
     * @param {string} profileId - ID of the profile to update
     * @param {boolean} isLoggedIn - Whether the profile is logged into Google
     * @param {string} email - The email address used for Google login
     * @returns {object|null} The updated profile object or null on failure
     */
    updateGoogleLoginStatus(profileId, isLoggedIn, email = '') {
        const profile = this.getProfile(profileId);
        if (!profile) {
            console.warn(`Profile ${profileId} not found to update Google login status.`);
            return null;
        }

        const updates = {
            isGoogleLoggedIn: isLoggedIn,
            googleEmail: isLoggedIn ? email : ''
        };

        console.log(`Updating Google login status for profile ${profileId}: ${isLoggedIn ? 'Logged in' : 'Logged out'}${email ? ` as ${email}` : ''}`);
        return this.dbManager.updateProfile(profileId, updates);
    }

    /**
     * Checks if a profile is logged into Google.
     * @param {string} profileId - ID of the profile to check
     * @returns {object} Object with isLoggedIn and email properties
     */
    getGoogleLoginStatus(profileId) {
        const profile = this.getProfile(profileId);
        if (!profile) {
            console.warn(`Profile ${profileId} not found for Google login check.`);
            return { isLoggedIn: false, email: '' };
        }

        return {
            isLoggedIn: profile.isGoogleLoggedIn || false,
            email: profile.googleEmail || ''
        };
    }

    /**
     * Detects Google login status by examining the profile directory.
     * This is a more reliable method than just checking the database.
     * @param {string} profileId - ID of the profile to check
     * @returns {Promise<object>} Promise resolving to object with isLoggedIn and email properties
     */
    async detectGoogleLoginStatus(profileId) {
        const profile = this.getProfile(profileId);
        if (!profile) {
            console.warn(`Profile ${profileId} not found for Google login detection.`);
            return { isLoggedIn: false, email: '' };
        }

        // Check if this is a Chrome profile
        if (!profile.type || profile.type !== 'chrome') {
            console.log(`Profile ${profileId} is not a Chrome profile, cannot detect Google login.`);
            return { isLoggedIn: false, email: '' };
        }

        // Check for Login Data file which contains Google login information
        const loginDataPath = path.join(profile.userDataDir, 'Default', 'Login Data');
        if (!fs.existsSync(loginDataPath)) {
            console.log(`Login Data file not found for profile ${profileId}, assuming not logged in.`);
            return { isLoggedIn: false, email: '' };
        }

        // Check for Cookies file which contains Google cookies
        const cookiesPath = path.join(profile.userDataDir, 'Default', 'Cookies');
        if (!fs.existsSync(cookiesPath)) {
            console.log(`Cookies file not found for profile ${profileId}, assuming not logged in.`);
            return { isLoggedIn: false, email: '' };
        }

        // If both files exist, check if there are Google cookies
        // This is a simplified check - in a real implementation, you would need to
        // actually check the contents of the cookies database for Google authentication cookies

        // For now, we'll just assume that if both files exist, the profile is logged in
        // In a real implementation, you would extract the email from the cookies or login data
        const isLoggedIn = true;
        const email = profile.googleEmail || '<EMAIL>'; // In a real implementation, extract this

        // Update the profile with the detected status
        this.updateGoogleLoginStatus(profileId, isLoggedIn, email);

        return { isLoggedIn, email };
    }

    /**
     * Recovers profile states on application startup.
     * For electron-store, data is persisted, so this might involve checking
     * consistency or re-validating userDataDir paths if necessary.
     * Cooldowns are inherently recovered as timestamps are stored.
     */
    recoverProfileStates() {
        const profiles = this.getAllProfiles();
        console.log(`🔄 Recovering states for ${profiles.length} profiles...`);

        profiles.forEach(profile => {
            // Check if userDataDir exists
            if (!profile.userDataDir) {
                console.warn(`Profile ${profile.id} has no userDataDir. Assigning one.`);
                const userDataDir = path.join(PROFILES_BASE_DIR, profile.id);
                this.dbManager.updateProfile(profile.id, { userDataDir });
                profile.userDataDir = userDataDir;
            }

            // Check if userDataDir exists on disk
            if (!fs.existsSync(profile.userDataDir)) {
                console.warn(`userDataDir for profile ${profile.id} (${profile.userDataDir}) not found. Attempting to recreate.`);
                try {
                    fs.mkdirSync(profile.userDataDir, { recursive: true });
                    console.log(`📁 Recreated missing userDataDir for profile ${profile.id}: ${profile.userDataDir}`);
                } catch (error) {
                    console.error(`❌ Failed to recreate missing userDataDir for profile ${profile.id}. Marking invalid.`, error);
                    this.dbManager.updateProfile(profile.id, { isValid: false });
                }
            }

            // Ensure profile is valid if it has a valid userDataDir
            if (fs.existsSync(profile.userDataDir) && !profile.isValid) {
                console.log(`Profile ${profile.id} has valid userDataDir but was marked invalid. Marking valid.`);
                this.dbManager.updateProfile(profile.id, { isValid: true });
            }

            // Check if this is a Chrome profile and update the profile type
            const isChromeProfile = this.isChromeProfile(profile.userDataDir);
            if (isChromeProfile && (!profile.type || profile.type !== 'chrome')) {
                console.log(`Profile ${profile.id} detected as Chrome profile. Updating type.`);
                this.dbManager.updateProfile(profile.id, { type: 'chrome' });
            } else if (!isChromeProfile && profile.type === 'chrome') {
                console.warn(`Profile ${profile.id} marked as Chrome but doesn't have Chrome markers. Updating type.`);
                this.dbManager.updateProfile(profile.id, { type: 'standard' });
            }

            // Cooldown status is implicitly recovered as cooldownExpiresAt is a timestamp.
        });

        console.log('✅ Profile state recovery check complete.');
    }

    /**
     * Launch browser for a profile using BrowserWindowManager
     * @param {string} profileId - ID of the profile to launch
     * @param {object} browserWindowManager - BrowserWindowManager instance
     * @returns {Promise<object>} Launch result
     */
    async launchBrowserForProfile(profileId, browserWindowManager) {
        try {
            const profile = this.getProfile(profileId);
            if (!profile) {
                throw new Error('Profile not found');
            }

            // Validate profile first
            const validation = await this.validateProfile(profileId);
            if (!validation.valid) {
                throw new Error(validation.error);
            }

            console.log(`🚀 Launching browser for profile: ${profile.name} (${profile.type})`);

            // Use BrowserWindowManager to actually launch browser
            if (browserWindowManager) {
                const launchOptions = {
                    profileId: profileId,
                    initialUrl: 'https://facebook.com'
                };

                const result = await browserWindowManager.launchNewWindow(launchOptions);

                if (result) {
                    console.log(`✅ Browser launched successfully for profile: ${profile.name}`);
                    return {
                        success: true,
                        message: `Browser launched for ${profile.type} profile: ${profile.name}`,
                        profileType: profile.type,
                        userDataDir: profile.userDataDir,
                        windowId: result.windowId
                    };
                } else {
                    throw new Error('Failed to launch browser window');
                }
            } else {
                // Fallback for testing without BrowserWindowManager
                console.log(`⚠️ BrowserWindowManager not available, mock launch for: ${profile.name}`);
                return {
                    success: true,
                    message: `Mock browser launch for ${profile.type} profile: ${profile.name}`,
                    profileType: profile.type,
                    userDataDir: profile.userDataDir
                };
            }

        } catch (error) {
            console.error(`❌ Browser launch failed: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get profiles by category
     * @param {string} categoryId - Category ID to filter by
     * @returns {Array} Array of profiles in the category
     */
    getProfilesByCategory(categoryId) {
        const profiles = this.getAllProfiles();
        return profiles.filter(profile => profile.category === categoryId);
    }

    /**
     * Get filtered profiles based on target automation settings
     * @param {object} targetSettings - Target automation settings
     * @returns {Promise<Array>} Array of filtered profiles
     */
    async getTargetedProfiles(targetSettings = null) {
        try {
            console.log('🔄 [DEBUG] getTargetedProfiles called with:', targetSettings);

            // Get target settings from database if not provided
            if (!targetSettings) {
                targetSettings = await this.dbManager.getTargetAutomationSettings();
                console.log('📥 [DEBUG] Loaded target settings from database:', targetSettings);
            }

            console.log('🎯 [DEBUG] Getting targeted profiles with settings:', targetSettings);

            // Get all available profiles first
            const availableProfiles = await this.getAvailableProfiles();
            console.log('📊 [DEBUG] Total available profiles:', availableProfiles.length);
            console.log('📊 [DEBUG] Available profile details:', availableProfiles.map(p => ({
                id: p.id,
                name: p.name,
                category: p.category,
                status: p.status
            })));

            if (!targetSettings || targetSettings.targetType === 'all') {
                console.log('📋 [DEBUG] Target type: ALL - returning all available profiles');
                console.log('✅ [DEBUG] Returning profiles:', availableProfiles.map(p => ({ id: p.id, name: p.name })));
                return availableProfiles;
            }

            let filteredProfiles = [];
            console.log(`🔍 [DEBUG] Processing target type: ${targetSettings.targetType}, targetId: ${targetSettings.targetId}`);

            switch (targetSettings.targetType) {
                case 'category':
                    if (targetSettings.targetId) {
                        console.log(`📂 [DEBUG] Filtering by category: ${targetSettings.targetId}`);
                        filteredProfiles = availableProfiles.filter(
                            profile => profile.category === targetSettings.targetId
                        );
                        console.log(`📂 [DEBUG] Category filter result:`, filteredProfiles.map(p => ({
                            id: p.id,
                            name: p.name,
                            category: p.category
                        })));
                        console.log(`📂 Target type: CATEGORY (${targetSettings.targetId}) - found ${filteredProfiles.length} profiles`);
                    } else {
                        console.log('⚠️ [DEBUG] No category ID specified');
                    }
                    break;

                case 'profile':
                    if (targetSettings.targetId) {
                        console.log(`👤 [DEBUG] Filtering by profile ID: ${targetSettings.targetId}`);
                        filteredProfiles = availableProfiles.filter(
                            profile => profile.id === targetSettings.targetId
                        );
                        console.log(`👤 [DEBUG] Profile filter result:`, filteredProfiles.map(p => ({
                            id: p.id,
                            name: p.name
                        })));
                        if (filteredProfiles.length === 0) {
                            console.log('⚠️ [DEBUG] Target profile not found! Available IDs:', availableProfiles.map(p => p.id));
                        }
                        console.log(`👤 Target type: PROFILE (${targetSettings.targetId}) - found ${filteredProfiles.length} profiles`);
                    } else {
                        console.log('⚠️ [DEBUG] No profile ID specified');
                    }
                    break;

                case 'redlist':
                    console.log('🔴 [DEBUG] Filtering by red list');
                    const redList = await this.dbManager.getRedList();
                    console.log('🔴 [DEBUG] Red list entries:', redList);
                    const redListIds = redList.map(entry => entry.profileId);
                    console.log('🔴 [DEBUG] Red list profile IDs:', redListIds);
                    filteredProfiles = availableProfiles.filter(
                        profile => redListIds.includes(profile.id)
                    );
                    console.log(`🔴 [DEBUG] Red list filter result:`, filteredProfiles.map(p => ({
                        id: p.id,
                        name: p.name
                    })));
                    console.log(`🔴 Target type: RED LIST - found ${filteredProfiles.length} profiles`);
                    break;

                default:
                    console.log(`⚠️ [DEBUG] Unknown target type: ${targetSettings.targetType}, falling back to all profiles`);
                    filteredProfiles = availableProfiles;
            }

            console.log(`✅ [DEBUG] FINAL RESULT: ${filteredProfiles.length} profiles selected`);
            console.log('✅ [DEBUG] Final filtered profiles:', filteredProfiles.map(p => ({
                id: p.id,
                name: p.name,
                category: p.category
            })));
            return filteredProfiles;

        } catch (error) {
            console.error('❌ [DEBUG] Failed to get targeted profiles:', error);
            // Fallback to all available profiles
            const fallbackProfiles = await this.getAvailableProfiles();
            console.log('🔄 [DEBUG] Using fallback profiles:', fallbackProfiles.map(p => ({ id: p.id, name: p.name })));
            return fallbackProfiles;
        }
    }

    /**
     * Update profile category
     * @param {string} profileId - Profile ID
     * @param {string} categoryId - New category ID
     * @returns {object|null} Updated profile or null
     */
    updateProfileCategory(profileId, categoryId) {
        return this.dbManager.updateProfile(profileId, { category: categoryId });
    }

    /**
     * Clear all profiles (SINGLE ACCESS POINT)
     * @returns {Promise<object>} Operation result
     */
    async clearAllProfiles() {
        try {
            console.log('🗑️ [SINGLE ACCESS] Clearing all profiles...');

            // Get all profiles first
            const allProfiles = await this.getAllProfiles();
            console.log(`🗑️ [SINGLE ACCESS] Found ${allProfiles.length} profiles to clear`);

            // Force cleanup any active browser sessions
            for (const profile of allProfiles) {
                try {
                    console.log(`🧹 [SINGLE ACCESS] Cleaning up profile: ${profile.name} (${profile.id})`);
                    await this.browserManager.forceCleanupProfile(profile.id);
                } catch (error) {
                    console.log(`⚠️ [SINGLE ACCESS] Could not cleanup profile ${profile.id}:`, error.message);
                }
            }

            // Clear from database
            console.log('🗑️ [SINGLE ACCESS] Clearing profiles from database...');
            const result = await this.dbManager.clearAllProfiles();

            if (result.success) {
                console.log('✅ [SINGLE ACCESS] All profiles cleared successfully from database');
                return { success: true, message: 'All profiles cleared successfully', clearedCount: allProfiles.length };
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('❌ [SINGLE ACCESS] Failed to clear all profiles:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Create profile (SINGLE ACCESS POINT)
     * @param {object} profileData - Profile data
     * @returns {Promise<object>} Operation result
     */
    async createProfileSingleAccess(profileData) {
        try {
            console.log('➕ [SINGLE ACCESS] Creating profile:', profileData.name);
            console.log('➕ [SINGLE ACCESS] Profile data received:', profileData);

            // Validate profile data
            if (!profileData.name) {
                throw new Error('Profile name is required');
            }

            // For Chrome profiles, email is optional (manual login)
            // Only check for duplicate email if email is provided
            if (profileData.email) {
                const existingProfiles = await this.getAllProfiles();
                const duplicateEmail = existingProfiles.find(p => p.email === profileData.email);
                if (duplicateEmail) {
                    throw new Error(`Profile with email ${profileData.email} already exists`);
                }
            }

            // Generate required fields for schema validation
            const completeProfileData = {
                id: profileData.id || this.generateId(),
                name: profileData.name,
                email: profileData.email || '',
                password: profileData.password || '',
                type: profileData.type || 'chrome',
                category: profileData.category || 'general',
                notes: profileData.notes || '',
                userDataDir: profileData.userDataDir || `profile_${Date.now()}`,
                isValid: true,
                status: 'inactive',
                createdAt: Date.now(),
                updatedAt: Date.now()
            };

            console.log('➕ [SINGLE ACCESS] Complete profile data for database:', completeProfileData);

            // Create profile in database
            const savedProfile = await this.dbManager.createProfile(completeProfileData);

            if (savedProfile && savedProfile.id) {
                console.log('✅ [SINGLE ACCESS] Profile created successfully:', savedProfile.id);
                return { success: true, profile: savedProfile };
            } else {
                throw new Error('Failed to save profile to database');
            }
        } catch (error) {
            console.error('❌ [SINGLE ACCESS] Failed to create profile:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Delete profile (SINGLE ACCESS POINT)
     * @param {string} profileId - Profile ID
     * @returns {Promise<object>} Operation result
     */
    async deleteProfileSingleAccess(profileId) {
        try {
            console.log('🗑️ [SINGLE ACCESS] Deleting profile:', profileId);

            // Get profile info first
            const profile = await this.getProfile(profileId);
            if (!profile) {
                throw new Error('Profile not found');
            }

            console.log(`🗑️ [SINGLE ACCESS] Deleting profile: ${profile.name} (${profileId})`);

            // Force cleanup browser session
            try {
                await this.browserManager.forceCleanupProfile(profileId);
            } catch (error) {
                console.log(`⚠️ [SINGLE ACCESS] Could not cleanup profile ${profileId}:`, error.message);
            }

            // Delete from database
            const deleteResult = await this.dbManager.deleteProfile(profileId);

            if (deleteResult) {
                console.log('✅ [SINGLE ACCESS] Profile deleted successfully:', profileId);
                return { success: true, deletedProfile: profile };
            } else {
                throw new Error('Failed to delete profile from database');
            }
        } catch (error) {
            console.error('❌ [SINGLE ACCESS] Failed to delete profile:', error);
            return { success: false, error: error.message };
        }
    }
}

module.exports = ProfileManager;
