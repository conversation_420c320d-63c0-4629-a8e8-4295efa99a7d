// Use real dependencies - no more mocks
const Store = require('electron-store');
const { v4: uuidv4 } = require('uuid');
console.log('✅ Using real electron-store and uuid');

const path = require('path');
const schemas = require('../store/schemas');

class DatabaseManager {
    constructor(storeName = 'facebot-data') {
        try {
            this.store = new Store({
                schema: schemas,
                name: storeName,
                cwd: path.join(process.cwd(), 'data')
            });
            console.log(`📄 Database initialized with electron-store. Storage path: ${this.store.path}`);
            this._ensureDefaultKeys();
            this.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize database:', error);
            throw error;
        }
    }

    _ensureDefaultKeys() {
        // electron-store handles default creation based on schema
        console.log('✅ Database schema initialized with default keys');
    }

    // Generic CRUD helpers
    _getAll(key) {
        return this.store.get(key, []);
    }

    _getById(key, id) {
        const items = this._getAll(key);
        return items.find(item => item.id === id) || null;
    }

    _save(key, itemData) {
        if (!itemData.id) {
            itemData.id = uuidv4();
        }
        if (!itemData.createdAt) {
            itemData.createdAt = Date.now();
        }
        itemData.updatedAt = Date.now();

        const items = this._getAll(key);
        const existingIndex = items.findIndex(p => p.id === itemData.id);

        if (existingIndex > -1) {
            items[existingIndex] = { ...items[existingIndex], ...itemData };
            items[existingIndex].updatedAt = Date.now();
            console.log(`✅ ${key} item updated: ${itemData.id}`);
        } else {
            items.push(itemData);
            console.log(`✅ ${key} item saved: ${itemData.id}`);
        }
        this.store.set(key, items);
        return this._getById(key, itemData.id);
    }

    _update(key, id, updates) {
        const items = this._getAll(key);
        const itemIndex = items.findIndex(p => p.id === id);

        if (itemIndex === -1) {
            console.error(`❌ ${key} item with id "${id}" not found for update.`);
            return null;
        }

        items[itemIndex] = { ...items[itemIndex], ...updates, updatedAt: Date.now() };
        this.store.set(key, items);
        console.log(`✅ ${key} item updated: ${id}`);
        return items[itemIndex];
    }

    _delete(key, id) {
        let items = this._getAll(key);
        const initialLength = items.length;
        items = items.filter(p => p.id !== id);

        if (items.length < initialLength) {
            this.store.set(key, items);
            console.log(`✅ ${key} item deleted: ${id}`);
            return true;
        }
        console.warn(`⚠️  ${key} item with id "${id}" not found for deletion.`);
        return false;
    }

    // --- Profiles ---
    getAllProfiles() { return this._getAll('profiles'); }
    getProfile(id) { return this._getById('profiles', id); }
    saveProfile(profileData) { return this._save('profiles', profileData); }
    updateProfile(id, updates) { return this._update('profiles', id, updates); }
    deleteProfile(id) { return this._delete('profiles', id); }

    // --- Proxies ---
    getAllProxies() { return this._getAll('proxies'); }
    getProxy(id) { return this._getById('proxies', id); }
    saveProxy(proxyData) { return this._save('proxies', proxyData); }
    updateProxy(id, updates) { return this._update('proxies', id, updates); }
    deleteProxy(id) { return this._delete('proxies', id); }

    // --- Campaigns ---
    getAllCampaigns() { return this._getAll('campaigns'); }
    getCampaign(id) { return this._getById('campaigns', id); }
    saveCampaign(campaignData) { return this._save('campaigns', campaignData); }
    updateCampaign(id, updates) { return this._update('campaigns', id, updates); }
    deleteCampaign(id) { return this._delete('campaigns', id); }

    // --- Logs ---
    getAllLogs() { return this._getAll('logs'); }
    getLog(id) { return this._getById('logs', id); }
    saveLog(logData) {
        if (!logData.timestamp) {
            logData.timestamp = Date.now();
        }
        return this._save('logs', logData);
    }
    deleteLog(id) { return this._delete('logs', id); }
    clearLogs() {
        this.store.set('logs', []);
        console.log('✅ All logs cleared');
        return true;
    }

    // --- Categories ---
    getAllCategories() { return this._getAll('categories'); }
    getCategory(id) { return this._getById('categories', id); }
    saveCategory(categoryData) { return this._save('categories', categoryData); }
    updateCategory(id, updates) { return this._update('categories', id, updates); }
    deleteCategory(id) { return this._delete('categories', id); }

    // --- Schedules ---
    getAllSchedules() { return this._getAll('schedules'); }
    getSchedule(id) { return this._getById('schedules', id); }
    saveSchedule(scheduleData) { return this._save('schedules', scheduleData); }
    updateSchedule(id, updates) { return this._update('schedules', id, updates); }
    deleteSchedule(id) { return this._delete('schedules', id); }

    // Category utility methods
    getProfilesByCategory(categoryId) {
        const profiles = this.getAllProfiles();
        return profiles.filter(profile => profile.category === categoryId);
    }

    getCategoryStats() {
        const profiles = this.getAllProfiles();
        const categories = this.getAllCategories();

        const stats = {};
        categories.forEach(category => {
            const categoryProfiles = profiles.filter(p => p.category === category.id);
            stats[category.id] = {
                name: category.name,
                color: category.color,
                totalProfiles: categoryProfiles.length,
                validProfiles: categoryProfiles.filter(p => p.isValid).length
            };
        });

        return stats;
    }

    // --- Settings ---
    getSettings() {
        return this.store.get('settings');
    }

    updateSettings(newSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = { ...currentSettings, ...newSettings };
        this.store.set('settings', updatedSettings);
        console.log('✅ Application settings updated');
        return updatedSettings;
    }

    // --- Schedule Settings ---
    getScheduleSettings() {
        const settings = this.getSettings();
        return settings.scheduleSettings || {
            defaultDelay: 30,
            maxConcurrent: 3,
            workStart: '09:00',
            workEnd: '17:00',
            weekendMode: 'normal'
        };
    }

    saveScheduleSettings(scheduleSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = {
            ...currentSettings,
            scheduleSettings: {
                ...currentSettings.scheduleSettings,
                ...scheduleSettings,
                updatedAt: Date.now()
            }
        };
        this.store.set('settings', updatedSettings);
        console.log('✅ Schedule settings updated');
        return { success: true };
    }

    // --- Automation Settings ---
    getAutomationSettings() {
        const settings = this.getSettings();
        return settings.automationSettings || {
            like: {
                enabled: true,
                delay: 3,
                maxPerSession: 50
            },
            comment: {
                enabled: true,
                delay: 5,
                maxPerSession: 20
            },
            shareTimeline: {
                enabled: true,
                delay: 10,
                maxPerSession: 10
            },
            shareGroups: {
                enabled: true,
                delay: 15,
                maxPerSession: 5
            }
        };
    }

    saveAutomationSettings(automationSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = {
            ...currentSettings,
            automationSettings: {
                ...currentSettings.automationSettings,
                ...automationSettings,
                updatedAt: Date.now()
            }
        };
        this.store.set('settings', updatedSettings);
        console.log('✅ Automation settings updated');
        return { success: true };
    }

    // --- Browser Settings ---
    getBrowserSettings() {
        const settings = this.getSettings();
        return settings.browserSettings || {
            engine: 'chromium',
            headless: false,
            userAgent: 'default',
            pageTimeout: 30000
        };
    }

    saveBrowserSettings(browserSettings) {
        const currentSettings = this.getSettings();
        const updatedSettings = {
            ...currentSettings,
            browserSettings: {
                ...currentSettings.browserSettings,
                ...browserSettings,
                updatedAt: Date.now()
            }
        };
        this.store.set('settings', updatedSettings);
        console.log('✅ Browser settings updated');
        return { success: true };
    }

    // --- Content Management ---
    getComments() {
        return this.store.get('content.comments', []);
    }

    addComment(text) {
        try {
            const comments = this.getComments();
            const newComment = {
                text: text,
                createdAt: Date.now(),
                id: Date.now().toString()
            };
            comments.push(newComment);
            this.store.set('content.comments', comments);
            console.log('✅ Comment added');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add comment:', error);
            return { success: false, error: error.message };
        }
    }

    addBulkComments(commentTexts) {
        try {
            const comments = this.getComments();
            const newComments = commentTexts.map(text => ({
                text: text,
                createdAt: Date.now(),
                id: Date.now().toString() + Math.random()
            }));
            comments.push(...newComments);
            this.store.set('content.comments', comments);
            console.log(`✅ ${newComments.length} comments added`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add bulk comments:', error);
            return { success: false, error: error.message };
        }
    }

    deleteComment(index) {
        try {
            const comments = this.getComments();
            if (index >= 0 && index < comments.length) {
                comments.splice(index, 1);
                this.store.set('content.comments', comments);
                console.log('✅ Comment deleted');
                return { success: true };
            } else {
                return { success: false, error: 'Invalid comment index' };
            }
        } catch (error) {
            console.error('❌ Failed to delete comment:', error);
            return { success: false, error: error.message };
        }
    }

    getLinks() {
        return this.store.get('content.links', []);
    }

    addLink(link) {
        try {
            const links = this.getLinks();
            const newLink = {
                ...link,
                createdAt: Date.now(),
                id: Date.now().toString()
            };
            links.push(newLink);
            this.store.set('content.links', links);
            console.log('✅ Link added');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add link:', error);
            return { success: false, error: error.message };
        }
    }

    addBulkLinks(linkData) {
        try {
            const links = this.getLinks();
            const newLinks = linkData.map(link => ({
                ...link,
                createdAt: Date.now(),
                id: Date.now().toString() + Math.random()
            }));
            links.push(...newLinks);
            this.store.set('content.links', links);
            console.log(`✅ ${newLinks.length} links added`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add bulk links:', error);
            return { success: false, error: error.message };
        }
    }

    deleteLink(index) {
        try {
            const links = this.getLinks();
            if (index >= 0 && index < links.length) {
                links.splice(index, 1);
                this.store.set('content.links', links);
                console.log('✅ Link deleted');
                return { success: true };
            } else {
                return { success: false, error: 'Invalid link index' };
            }
        } catch (error) {
            console.error('❌ Failed to delete link:', error);
            return { success: false, error: error.message };
        }
    }

    getGroupLinks() {
        return this.store.get('content.groupLinks', []);
    }

    addGroupLink(group) {
        try {
            const groupLinks = this.getGroupLinks();
            const newGroup = {
                ...group,
                createdAt: Date.now(),
                id: Date.now().toString()
            };
            groupLinks.push(newGroup);
            this.store.set('content.groupLinks', groupLinks);
            console.log('✅ Group link added');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add group link:', error);
            return { success: false, error: error.message };
        }
    }

    addBulkGroupLinks(groupData) {
        try {
            const groupLinks = this.getGroupLinks();
            const newGroups = groupData.map(group => ({
                ...group,
                createdAt: Date.now(),
                id: Date.now().toString() + Math.random()
            }));
            groupLinks.push(...newGroups);
            this.store.set('content.groupLinks', groupLinks);
            console.log(`✅ ${newGroups.length} group links added`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add bulk group links:', error);
            return { success: false, error: error.message };
        }
    }

    deleteGroupLink(index) {
        try {
            const groupLinks = this.getGroupLinks();
            if (index >= 0 && index < groupLinks.length) {
                groupLinks.splice(index, 1);
                this.store.set('content.groupLinks', groupLinks);
                console.log('✅ Group link deleted');
                return { success: true };
            } else {
                return { success: false, error: 'Invalid group link index' };
            }
        } catch (error) {
            console.error('❌ Failed to delete group link:', error);
            return { success: false, error: error.message };
        }
    }

    getDecoyLinks() {
        return this.store.get('content.decoyLinks', []);
    }

    addDecoyLink(link) {
        try {
            const decoyLinks = this.getDecoyLinks();
            const newLink = {
                ...link,
                createdAt: Date.now(),
                id: Date.now().toString()
            };
            decoyLinks.push(newLink);
            this.store.set('content.decoyLinks', decoyLinks);
            console.log('✅ Decoy link added');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add decoy link:', error);
            return { success: false, error: error.message };
        }
    }

    addBulkDecoyLinks(linkData) {
        try {
            const decoyLinks = this.getDecoyLinks();
            const newLinks = linkData.map(link => ({
                ...link,
                createdAt: Date.now(),
                id: Date.now().toString() + Math.random()
            }));
            decoyLinks.push(...newLinks);
            this.store.set('content.decoyLinks', decoyLinks);
            console.log(`✅ ${newLinks.length} decoy links added`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add bulk decoy links:', error);
            return { success: false, error: error.message };
        }
    }

    deleteDecoyLink(index) {
        try {
            const decoyLinks = this.getDecoyLinks();
            if (index >= 0 && index < decoyLinks.length) {
                decoyLinks.splice(index, 1);
                this.store.set('content.decoyLinks', decoyLinks);
                console.log('✅ Decoy link deleted');
                return { success: true };
            } else {
                return { success: false, error: 'Invalid decoy link index' };
            }
        } catch (error) {
            console.error('❌ Failed to delete decoy link:', error);
            return { success: false, error: error.message };
        }
    }

    // Content Status Updates
    updateCommentsStatus(statusUpdates) {
        try {
            const comments = this.getComments();
            statusUpdates.forEach(update => {
                if (update.index >= 0 && update.index < comments.length) {
                    comments[update.index].active = update.active;
                }
            });
            this.store.set('content.comments', comments);
            console.log('✅ Comments status updated');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to update comments status:', error);
            return { success: false, error: error.message };
        }
    }

    updateLinksStatus(statusUpdates) {
        try {
            const links = this.getLinks();
            statusUpdates.forEach(update => {
                if (update.index >= 0 && update.index < links.length) {
                    links[update.index].active = update.active;
                }
            });
            this.store.set('content.links', links);
            console.log('✅ Links status updated');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to update links status:', error);
            return { success: false, error: error.message };
        }
    }

    updateGroupLinksStatus(statusUpdates) {
        try {
            const groupLinks = this.getGroupLinks();
            statusUpdates.forEach(update => {
                if (update.index >= 0 && update.index < groupLinks.length) {
                    groupLinks[update.index].active = update.active;
                }
            });
            this.store.set('content.groupLinks', groupLinks);
            console.log('✅ Group links status updated');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to update group links status:', error);
            return { success: false, error: error.message };
        }
    }

    updateDecoyLinksStatus(statusUpdates) {
        try {
            const decoyLinks = this.getDecoyLinks();
            statusUpdates.forEach(update => {
                if (update.index >= 0 && update.index < decoyLinks.length) {
                    decoyLinks[update.index].active = update.active;
                }
            });
            this.store.set('content.decoyLinks', decoyLinks);
            console.log('✅ Decoy links status updated');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to update decoy links status:', error);
            return { success: false, error: error.message };
        }
    }

    // Target Automation Settings
    saveTargetAutomationSettings(settings) {
        try {
            this.store.set('targetAutomationSettings', settings);
            console.log('✅ Target automation settings saved');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to save target automation settings:', error);
            return { success: false, error: error.message };
        }
    }

    getTargetAutomationSettings() {
        return this.store.get('targetAutomationSettings', {
            targetType: 'all',
            targetId: null,
            decoySettings: {
                count: 2,
                delay: 5000
            }
        });
    }

    // Clear all profiles (SINGLE ACCESS POINT)
    clearAllProfiles() {
        try {
            console.log('🗑️ [DATABASE] Clearing all profiles...');
            this.store.set('profiles', []);
            console.log('✅ [DATABASE] All profiles cleared from database');
            return { success: true };
        } catch (error) {
            console.error('❌ [DATABASE] Failed to clear all profiles:', error);
            return { success: false, error: error.message };
        }
    }

    // Red List Management (for failed profiles)
    getRedList() {
        return this.store.get('redList', []);
    }

    addToRedList(profileId, reason = 'Automation failed') {
        try {
            const redList = this.getRedList();
            const existingEntry = redList.find(entry => entry.profileId === profileId);

            if (existingEntry) {
                existingEntry.lastFailure = Date.now();
                existingEntry.failureCount += 1;
                existingEntry.reason = reason;
            } else {
                redList.push({
                    profileId,
                    reason,
                    addedAt: Date.now(),
                    lastFailure: Date.now(),
                    failureCount: 1
                });
            }

            this.store.set('redList', redList);
            console.log(`✅ Profile ${profileId} added to red list`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to add to red list:', error);
            return { success: false, error: error.message };
        }
    }

    removeFromRedList(profileId) {
        try {
            const redList = this.getRedList();
            const filteredList = redList.filter(entry => entry.profileId !== profileId);
            this.store.set('redList', filteredList);
            console.log(`✅ Profile ${profileId} removed from red list`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to remove from red list:', error);
            return { success: false, error: error.message };
        }
    }

    clearRedList() {
        try {
            this.store.set('redList', []);
            console.log('✅ Red list cleared');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to clear red list:', error);
            return { success: false, error: error.message };
        }
    }

    // Utility methods
    resetStoreKey(key) {
        if (schemas[key] && schemas[key].hasOwnProperty('default')) {
            this.store.set(key, schemas[key].default);
            console.log(`✅ Store key '${key}' has been reset to its default value.`);
            return true;
        }
        console.warn(`⚠️  Cannot reset key '${key}': No schema default found or key is invalid.`);
        return false;
    }

    clearAllData() {
        this.store.clear();
        console.warn('⚠️  All data in electron-store has been cleared.');
        this._ensureDefaultKeys();
    }

    // Legacy compatibility methods (for backward compatibility)
    async createProfile(profileData) {
        return this.saveProfile(profileData);
    }

    async createProxy(proxyData) {
        return this.saveProxy(proxyData);
    }

    async createCampaign(campaignData) {
        return this.saveCampaign(campaignData);
    }

    async addLog(logData) {
        return this.saveLog(logData);
    }

    async getLogs(filters = {}) {
        let logs = this.getAllLogs();

        // Apply filters
        if (filters.profileId) {
            logs = logs.filter(log => log.profileId === filters.profileId);
        }

        if (filters.campaignId) {
            logs = logs.filter(log => log.campaignId === filters.campaignId);
        }

        if (filters.status) {
            logs = logs.filter(log => log.status === filters.status);
        }

        if (filters.fromDate) {
            logs = logs.filter(log => log.timestamp >= filters.fromDate);
        }

        if (filters.toDate) {
            logs = logs.filter(log => log.timestamp <= filters.toDate);
        }

        // Sort by timestamp descending
        logs.sort((a, b) => b.timestamp - a.timestamp);

        // Apply limit
        if (filters.limit) {
            logs = logs.slice(0, filters.limit);
        }

        return logs;
    }

    // --- Content Import/Export Methods ---
    clearComments() {
        try {
            this.store.set('content.comments', []);
            console.log('✅ All comments cleared');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to clear comments:', error);
            return { success: false, error: error.message };
        }
    }

    clearLinks() {
        try {
            this.store.set('content.links', []);
            console.log('✅ All links cleared');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to clear links:', error);
            return { success: false, error: error.message };
        }
    }

    clearGroupLinks() {
        try {
            this.store.set('content.groupLinks', []);
            console.log('✅ All group links cleared');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to clear group links:', error);
            return { success: false, error: error.message };
        }
    }

    clearDecoyLinks() {
        try {
            this.store.set('content.decoyLinks', []);
            console.log('✅ All decoy links cleared');
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to clear decoy links:', error);
            return { success: false, error: error.message };
        }
    }

    importComments(comments) {
        try {
            const existingComments = this.getComments();
            const newComments = comments.map(comment => ({
                ...comment,
                id: comment.id || Date.now().toString() + Math.random(),
                createdAt: comment.createdAt || Date.now()
            }));
            existingComments.push(...newComments);
            this.store.set('content.comments', existingComments);
            console.log(`✅ ${newComments.length} comments imported`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to import comments:', error);
            return { success: false, error: error.message };
        }
    }

    importLinks(links) {
        try {
            const existingLinks = this.getLinks();
            const newLinks = links.map(link => ({
                ...link,
                id: link.id || Date.now().toString() + Math.random(),
                createdAt: link.createdAt || Date.now()
            }));
            existingLinks.push(...newLinks);
            this.store.set('content.links', existingLinks);
            console.log(`✅ ${newLinks.length} links imported`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to import links:', error);
            return { success: false, error: error.message };
        }
    }

    importGroupLinks(groupLinks) {
        try {
            const existingGroupLinks = this.getGroupLinks();
            const newGroupLinks = groupLinks.map(group => ({
                ...group,
                id: group.id || Date.now().toString() + Math.random(),
                createdAt: group.createdAt || Date.now()
            }));
            existingGroupLinks.push(...newGroupLinks);
            this.store.set('content.groupLinks', existingGroupLinks);
            console.log(`✅ ${newGroupLinks.length} group links imported`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to import group links:', error);
            return { success: false, error: error.message };
        }
    }

    importDecoyLinks(decoyLinks) {
        try {
            const existingDecoyLinks = this.getDecoyLinks();
            const newDecoyLinks = decoyLinks.map(link => ({
                ...link,
                id: link.id || Date.now().toString() + Math.random(),
                createdAt: link.createdAt || Date.now()
            }));
            existingDecoyLinks.push(...newDecoyLinks);
            this.store.set('content.decoyLinks', existingDecoyLinks);
            console.log(`✅ ${newDecoyLinks.length} decoy links imported`);
            return { success: true };
        } catch (error) {
            console.error('❌ Failed to import decoy links:', error);
            return { success: false, error: error.message };
        }
    }

    // Close method (for electron-store, no actual closing needed)
    close() {
        console.log('✅ Database connection closed (electron-store)');
    }


}

module.exports = DatabaseManager;
