const { EventEmitter } = require('events');

// Use real UUID - no more mocks
const { v4: uuidv4 } = require('uuid');

const FacebookBot = require('./FacebookBot');
const SmartSelectorManager = require('./SmartSelectorManager');
const FacebookAutomation = require('../automation/FacebookAutomation');

class AutomationController extends EventEmitter {
    constructor(dbManager, profileManager, browserWindowManager) {
        super();
        this.dbManager = dbManager;
        this.profileManager = profileManager;
        this.browserWindowManager = browserWindowManager;
        this.activeCampaigns = new Map();
        this.campaignQueues = new Map();

        // Initialize smart selector manager and Facebook automation
        this.smartSelectorManager = new SmartSelectorManager(dbManager);
        this.facebookAutomation = new FacebookAutomation(profileManager, dbManager, this.smartSelectorManager);

        // Initialize BrowserConnector
        const BrowserConnector = require('./BrowserConnector');
        this.browserConnector = new BrowserConnector();
        this.browserConnector.initialize(this.browserWindowManager);
    }

    async startCampaign(campaignConfig) {
        try {
            const campaignId = uuidv4();

            // Validate campaign config
            this.validateCampaignConfig(campaignConfig);

            // Get available profiles
            const availableProfiles = await this.profileManager.getAvailableProfiles();

            if (availableProfiles.length === 0) {
                throw new Error('No available profiles for automation');
            }

            // Filter profiles based on campaign selection
            let selectedProfiles = availableProfiles;
            if (campaignConfig.profileIds && campaignConfig.profileIds.length > 0) {
                selectedProfiles = availableProfiles.filter(
                    profile => campaignConfig.profileIds.includes(profile.id)
                );
            }

            if (selectedProfiles.length === 0) {
                throw new Error('No selected profiles are available');
            }

            // Create campaign in database
            const campaign = await this.dbManager.createCampaign({
                name: campaignConfig.name || `Campaign ${Date.now()}`,
                config: campaignConfig,
                totalProfiles: selectedProfiles.length
            });

            // Create campaign execution context
            const campaignContext = {
                id: campaignId,
                dbId: campaign.id,
                config: campaignConfig,
                profiles: selectedProfiles,
                status: 'running',
                startedAt: Date.now(),
                completedProfiles: 0,
                successfulActions: 0,
                failedActions: 0,
                currentBatch: 0,
                totalBatches: Math.ceil(selectedProfiles.length / (campaignConfig.batchSize || 5))
            };

            this.activeCampaigns.set(campaignId, campaignContext);

            // Update campaign status in database
            await this.dbManager.updateCampaign(campaign.id, {
                status: 'running',
                startedAt: Date.now()
            });

            // Start campaign execution
            this.executeCampaign(campaignContext);

            console.log(`Campaign started: ${campaignId} with ${selectedProfiles.length} profiles`);

            // Emit campaign started event
            this.emit('campaignStarted', {
                campaignId,
                profileCount: selectedProfiles.length,
                config: campaignConfig
            });

            return {
                success: true,
                campaignId,
                profileCount: selectedProfiles.length,
                estimatedDuration: this.estimateCampaignDuration(campaignContext)
            };
        } catch (error) {
            console.error('Failed to start campaign:', error);
            throw error;
        }
    }

    async stopCampaign(campaignId) {
        try {
            const campaign = this.activeCampaigns.get(campaignId);
            if (!campaign) {
                throw new Error('Campaign not found or not running');
            }

            campaign.status = 'stopping';

            // Stop any running batches
            if (this.campaignQueues.has(campaignId)) {
                clearTimeout(this.campaignQueues.get(campaignId));
                this.campaignQueues.delete(campaignId);
            }

            // Update database
            await this.dbManager.updateCampaign(campaign.dbId, {
                status: 'stopped',
                completedAt: Date.now(),
                completedProfiles: campaign.completedProfiles,
                successfulActions: campaign.successfulActions,
                failedActions: campaign.failedActions
            });

            // Clean up active profiles
            campaign.profiles.forEach(profile => {
                this.profileManager.markProfileInactive(profile.id);
            });

            this.activeCampaigns.delete(campaignId);

            console.log(`Campaign stopped: ${campaignId}`);

            // Emit campaign stopped event
            this.emit('campaignStopped', { campaignId });

            return { success: true };
        } catch (error) {
            console.error('Failed to stop campaign:', error);
            throw error;
        }
    }

    async getCampaignStatus(campaignId) {
        try {
            const campaign = this.activeCampaigns.get(campaignId);
            if (!campaign) {
                // Check if campaign exists in database
                const dbCampaign = await this.dbManager.getCampaign(campaignId);
                if (dbCampaign) {
                    return {
                        id: campaignId,
                        status: dbCampaign.status,
                        completedProfiles: dbCampaign.completedProfiles,
                        totalProfiles: dbCampaign.totalProfiles,
                        successfulActions: dbCampaign.successfulActions,
                        failedActions: dbCampaign.failedActions,
                        startedAt: dbCampaign.startedAt,
                        completedAt: dbCampaign.completedAt
                    };
                }
                throw new Error('Campaign not found');
            }

            return {
                id: campaignId,
                status: campaign.status,
                completedProfiles: campaign.completedProfiles,
                totalProfiles: campaign.profiles.length,
                successfulActions: campaign.successfulActions,
                failedActions: campaign.failedActions,
                currentBatch: campaign.currentBatch,
                totalBatches: campaign.totalBatches,
                startedAt: campaign.startedAt,
                progress: (campaign.completedProfiles / campaign.profiles.length) * 100
            };
        } catch (error) {
            console.error('Failed to get campaign status:', error);
            throw error;
        }
    }

    async executeCampaign(campaign) {
        try {
            const batchSize = campaign.config.batchSize || 5;
            const batchInterval = (campaign.config.batchInterval || 60) * 1000; // Convert to milliseconds

            // Process profiles in batches
            for (let i = 0; i < campaign.profiles.length; i += batchSize) {
                if (campaign.status !== 'running') {
                    break;
                }

                const batch = campaign.profiles.slice(i, i + batchSize);
                campaign.currentBatch = Math.floor(i / batchSize) + 1;

                console.log(`Processing batch ${campaign.currentBatch}/${campaign.totalBatches} for campaign ${campaign.id}`);

                // Execute batch
                await this.executeBatch(campaign, batch);

                // Wait for batch interval before next batch (except for last batch)
                if (i + batchSize < campaign.profiles.length && campaign.status === 'running') {
                    console.log(`Waiting ${batchInterval / 1000}s before next batch...`);
                    await new Promise(resolve => {
                        const timeoutId = setTimeout(resolve, batchInterval);
                        this.campaignQueues.set(campaign.id, timeoutId);
                    });
                    this.campaignQueues.delete(campaign.id);
                }
            }

            // Campaign completed
            if (campaign.status === 'running') {
                campaign.status = 'completed';

                await this.dbManager.updateCampaign(campaign.dbId, {
                    status: 'completed',
                    completedAt: Date.now(),
                    completedProfiles: campaign.completedProfiles,
                    successfulActions: campaign.successfulActions,
                    failedActions: campaign.failedActions
                });

                console.log(`Campaign completed: ${campaign.id}`);

                // Emit campaign completed event
                this.emit('campaignCompleted', {
                    campaignId: campaign.id,
                    completedProfiles: campaign.completedProfiles,
                    successfulActions: campaign.successfulActions,
                    failedActions: campaign.failedActions
                });
            }

            // Clean up
            this.activeCampaigns.delete(campaign.id);
            campaign.profiles.forEach(profile => {
                this.profileManager.markProfileInactive(profile.id);
            });

        } catch (error) {
            console.error(`Campaign execution failed: ${campaign.id}`, error);

            // Mark campaign as failed
            campaign.status = 'failed';
            await this.dbManager.updateCampaign(campaign.dbId, {
                status: 'failed',
                completedAt: Date.now()
            });

            this.emit('campaignFailed', { campaignId: campaign.id, error: error.message });
        }
    }

    async executeBatch(campaign, profiles) {
        const promises = profiles.map(profile => this.executeProfileActions(campaign, profile));
        await Promise.allSettled(promises);
    }

    async executeProfileActions(campaign, profile) {
        let bot = null;
        try {
            // Mark profile as active
            this.profileManager.markProfileActive(profile.id);

            // Get proxy if assigned
            let proxy = null;
            if (profile.proxyId) {
                proxy = await this.dbManager.getProxy(profile.proxyId);
            }

            // Create and initialize bot
            bot = new FacebookBot(profile, proxy, this.dbManager);
            await bot.initialize();

            // Login
            await bot.login(profile.email, profile.password);

            const config = campaign.config;
            const facebookSettings = profile.facebook || {};

            // Execute actions based on configuration
            if (config.actions.includes('comment') && facebookSettings.settings?.enableComments !== false) {
                await this.executeCommentAction(bot, profile, config, facebookSettings);
            }

            if (config.actions.includes('share') && facebookSettings.settings?.enableShares !== false) {
                await this.executeShareAction(bot, profile, config, facebookSettings);
            }

            if (config.actions.includes('like') && facebookSettings.settings?.enableLikes !== false) {
                await this.executeLikeAction(bot, profile, config);
            }

            if (config.actions.includes('decoy') && facebookSettings.settings?.enableDecoyLinks !== false) {
                await this.executeDecoyAction(bot, profile, config, facebookSettings);
            }

            // Logout
            await bot.logout();

            // Mark profile as used
            const cooldownMinutes = config.cooldownMinutes || 60;
            await this.profileManager.markProfileAsUsed(profile.id, cooldownMinutes);

            campaign.completedProfiles++;

            // Emit progress update
            this.emit('campaignProgress', {
                campaignId: campaign.id,
                completedProfiles: campaign.completedProfiles,
                totalProfiles: campaign.profiles.length,
                currentProfile: profile.name
            });

        } catch (error) {
            console.error(`Profile execution failed: ${profile.name}`, error.message);
            campaign.failedActions++;

            // Log the error
            await this.dbManager.addLog({
                profileId: profile.id,
                action: 'campaign_execution',
                target: campaign.config.postUrl || '',
                status: 'failed',
                message: error.message,
                campaignId: campaign.dbId
            });
        } finally {
            // Clean up
            if (bot) {
                await bot.cleanup();
            }
            this.profileManager.markProfileInactive(profile.id);
        }
    }

    async executeCommentAction(bot, profile, config, facebookSettings) {
        if (!config.postId) return;

        const comments = facebookSettings.comments || [];
        if (comments.length === 0) return;

        const randomComment = comments[Math.floor(Math.random() * comments.length)];
        await bot.createComment(config.postId, randomComment);

        // Add delay
        const delay = (facebookSettings.settings?.delayComment || 4) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));

        const campaign = this.activeCampaigns.get(bot.profile.campaignId);
        if (campaign) campaign.successfulActions++;
    }

    async executeShareAction(bot, profile, config, facebookSettings) {
        if (!config.postUrl) return;

        const shares = facebookSettings.shares || [];
        const shareText = shares.length > 0 ?
            shares[Math.floor(Math.random() * shares.length)] : '';

        await bot.sharePost(config.postUrl, shareText);

        // Add delay
        const delay = (facebookSettings.settings?.delayShare || 7) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));

        const campaign = this.activeCampaigns.get(bot.profile.campaignId);
        if (campaign) campaign.successfulActions++;
    }

    async executeLikeAction(bot, profile, config) {
        if (!config.postUrl) return;

        await bot.performLike(config.postUrl);

        // Add delay
        const delay = 2000 + Math.random() * 3000; // 2-5 seconds
        await new Promise(resolve => setTimeout(resolve, delay));

        const campaign = this.activeCampaigns.get(bot.profile.campaignId);
        if (campaign) campaign.successfulActions++;
    }

    async executeDecoyAction(bot, profile, config, facebookSettings) {
        const decoyLinks = facebookSettings.decoyLinks || [];
        if (decoyLinks.length === 0) return;

        const stayInterval = config.decoyStayInterval || 30000; // 30 seconds default
        await bot.visitDecoyLinks(decoyLinks, stayInterval);

        const campaign = this.activeCampaigns.get(bot.profile.campaignId);
        if (campaign) campaign.successfulActions++;
    }

    validateCampaignConfig(config) {
        if (!config.actions || !Array.isArray(config.actions) || config.actions.length === 0) {
            throw new Error('Campaign must have at least one action');
        }

        const validActions = ['comment', 'share', 'like', 'decoy'];
        const invalidActions = config.actions.filter(action => !validActions.includes(action));
        if (invalidActions.length > 0) {
            throw new Error(`Invalid actions: ${invalidActions.join(', ')}`);
        }

        if (config.actions.includes('comment') && !config.postId) {
            throw new Error('Post ID is required for comment action');
        }

        if ((config.actions.includes('share') || config.actions.includes('like')) && !config.postUrl) {
            throw new Error('Post URL is required for share/like actions');
        }
    }

    estimateCampaignDuration(campaign) {
        const avgActionTime = 30; // seconds per action
        const actionsPerProfile = campaign.config.actions.length;
        const totalTime = campaign.profiles.length * actionsPerProfile * avgActionTime;
        const batchTime = Math.ceil(campaign.profiles.length / (campaign.config.batchSize || 5)) *
                         (campaign.config.batchInterval || 60);

        return totalTime + batchTime; // in seconds
    }

    async stopAllCampaigns() {
        const campaignIds = Array.from(this.activeCampaigns.keys());
        await Promise.all(campaignIds.map(id => this.stopCampaign(id)));
    }

    getActiveCampaigns() {
        return Array.from(this.activeCampaigns.values()).map(campaign => ({
            id: campaign.id,
            status: campaign.status,
            completedProfiles: campaign.completedProfiles,
            totalProfiles: campaign.profiles.length,
            currentBatch: campaign.currentBatch,
            totalBatches: campaign.totalBatches
        }));
    }

    /**
     * Start Facebook group sharing automation
     * @param {object} options - Sharing options
     * @returns {Promise<object>} Automation result
     */
    async startGroupSharing(options) {
        try {
            console.log('🚀 Starting Facebook group sharing automation...');

            const result = await this.facebookAutomation.shareToGroups(options);

            if (result.success) {
                // Emit automation event
                this.emit('automationStarted', {
                    type: 'group_sharing',
                    automationId: result.results.automationId,
                    profileId: options.profileId,
                    totalGroups: options.groups.length
                });
            }

            return result;
        } catch (error) {
            console.error('Failed to start group sharing:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get automation status
     * @param {string} automationId - Automation ID
     * @returns {object|null} Automation status
     */
    getAutomationStatus(automationId) {
        return this.facebookAutomation.getAutomationStatus(automationId);
    }

    /**
     * Stop automation
     * @param {string} automationId - Automation ID
     * @returns {boolean} Success status
     */
    stopAutomation(automationId) {
        return this.facebookAutomation.stopAutomation(automationId);
    }

    /**
     * Get all active automations
     * @returns {Array} Array of active automations
     */
    getActiveAutomations() {
        return this.facebookAutomation.getActiveAutomations();
    }

    /**
     * Get smart selector statistics
     * @returns {object} Selector statistics
     */
    getSmartSelectorStats() {
        return this.smartSelectorManager.getStats();
    }

    /**
     * Launch browser for specific profile using BrowserWindowManager
     * @param {object} profile - Profile object
     * @returns {Promise<object>} Launch result with context
     */
    async launchBrowserForProfile(profile) {
        try {
            console.log(`🚀 Launching browser for automation: ${profile.name} (${profile.id})`);

            // Use BrowserWindowManager to launch browser with blank page first
            const launchResult = await this.browserWindowManager.launchNewWindow({
                profileId: profile.id,
                initialUrl: 'about:blank'  // Start with blank page to avoid timeout
            });

            if (!launchResult) {
                throw new Error('Failed to launch browser window');
            }

            console.log(`✅ Browser launched for automation: ${profile.name}`);

            return {
                success: true,
                context: launchResult.context,
                windowId: launchResult.windowId,
                profileId: profile.id
            };

        } catch (error) {
            console.error(`❌ Browser launch failed for automation: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * Run Like automation
     * @param {object} options - Like automation options
     * @returns {Promise<object>} Automation result
     */
    async runLikeAutomation(options = {}) {
        try {
            console.log('🚀 [DEBUG] Starting Like automation...');
            console.log('🚀 [DEBUG] Like automation options received:', options);

            // Use target settings from options if provided, otherwise get from database
            const targetSettings = options.targetSettings || null;
            console.log('🎯 [DEBUG] Target settings for Like automation:', targetSettings);

            // Get targeted profiles based on automation settings
            const profiles = await this.profileManager.getTargetedProfiles(targetSettings);
            console.log(`📊 [DEBUG] getTargetedProfiles returned ${profiles.length} profiles`);

            if (profiles.length === 0) {
                console.log('❌ [DEBUG] No profiles found for Like automation');
                throw new Error('No available profiles for automation based on target settings');
            }

            console.log(`📋 Found ${profiles.length} targeted profiles for Like automation`);
            console.log('📋 [DEBUG] Profile details:', profiles.map(p => ({ id: p.id, name: p.name })));

            const profile = profiles[0];
            console.log(`🎯 [DEBUG] Using profile for Like automation: ${profile.name} (${profile.id})`);

            // Check if profile browser is already open in BrowserWindowManager
            let browserContext = null;
            let shouldCleanupBrowser = false;

            // Try to get existing browser context from BrowserWindowManager
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const existingWindow = activeWindows.find(w => w.profileId === profile.id);

            if (existingWindow) {
                // Use existing browser context
                console.log('🔄 [DEBUG] Using existing browser context for profile:', profile.id);
                browserContext = this.browserWindowManager.getWindowContext(existingWindow.windowId);
                if (!browserContext) {
                    console.log('⚠️ [DEBUG] Existing browser context not found, launching new one');
                    const launchResult = await this.launchBrowserForProfile(profile);
                    if (!launchResult || !launchResult.success) {
                        throw new Error(launchResult?.error || 'Failed to launch browser');
                    }
                    browserContext = launchResult.context;
                    shouldCleanupBrowser = true;
                }
            } else {
                // Launch new browser for automation
                console.log('🚀 [DEBUG] Launching new browser for automation');
                const launchResult = await this.launchBrowserForProfile(profile);
                if (!launchResult || !launchResult.success) {
                    throw new Error(launchResult?.error || 'Failed to launch browser');
                }
                browserContext = launchResult.context;
                shouldCleanupBrowser = true;
            }

            // Perform decoy actions if configured
            if (targetSettings && targetSettings.decoySettings && targetSettings.decoySettings.count > 0) {
                console.log('🎭 [DEBUG] Performing decoy actions...');
                await this.performDecoyActions(profile.id, targetSettings.decoySettings);
            }

            // Perform actual like automation
            console.log('❤️ [DEBUG] Starting like automation...');
            const result = await this.performLikeActions(profile.id, targetSettings);

            // Cleanup only if we launched a new browser
            if (shouldCleanupBrowser) {
                console.log('🧹 [DEBUG] Cleaning up automation browser');
                const activeWindows = this.browserWindowManager.getActiveWindows();
                const window = activeWindows.find(w => w.profileId === profile.id);
                if (window) {
                    await this.browserWindowManager.closeWindow(window.windowId);
                }
            } else {
                console.log('🔄 [DEBUG] Keeping existing browser open');
            }

            return result;
        } catch (error) {
            console.error('❌ [DEBUG] Like automation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Run Comment automation
     * @param {object} options - Comment automation options
     * @returns {Promise<object>} Automation result
     */
    async runCommentAutomation(options = {}) {
        try {
            console.log('🚀 [DEBUG] Starting Comment automation...');
            console.log('🚀 [DEBUG] Comment automation options received:', options);

            // Use target settings from options if provided, otherwise get from database
            const targetSettings = options.targetSettings || null;
            console.log('🎯 [DEBUG] Target settings for Comment automation:', targetSettings);

            // Get targeted profiles based on automation settings
            const profiles = await this.profileManager.getTargetedProfiles(targetSettings);
            console.log(`📊 [DEBUG] getTargetedProfiles returned ${profiles.length} profiles`);

            if (profiles.length === 0) {
                console.log('❌ [DEBUG] No profiles found for Comment automation');
                throw new Error('No available profiles for automation based on target settings');
            }

            console.log(`📋 Found ${profiles.length} targeted profiles for Comment automation`);
            console.log('📋 [DEBUG] Profile details:', profiles.map(p => ({ id: p.id, name: p.name })));

            const profile = profiles[0];
            console.log(`🎯 [DEBUG] Using profile for Comment automation: ${profile.name} (${profile.id})`);

            // Check if profile browser is already open in BrowserWindowManager
            let browserContext = null;
            let shouldCleanupBrowser = false;

            // Try to get existing browser context from BrowserWindowManager
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const existingWindow = activeWindows.find(w => w.profileId === profile.id);

            if (existingWindow) {
                // Use existing browser context
                console.log('🔄 [DEBUG] Using existing browser context for profile:', profile.id);
                browserContext = this.browserWindowManager.getWindowContext(existingWindow.windowId);
                if (!browserContext) {
                    console.log('⚠️ [DEBUG] Existing browser context not found, launching new one');
                    const launchResult = await this.launchBrowserForProfile(profile);
                    if (!launchResult || !launchResult.success) {
                        throw new Error(launchResult?.error || 'Failed to launch browser');
                    }
                    browserContext = launchResult.context;
                    shouldCleanupBrowser = true;
                }
            } else {
                // Launch new browser for automation
                console.log('🚀 [DEBUG] Launching new browser for automation');
                const launchResult = await this.launchBrowserForProfile(profile);
                if (!launchResult || !launchResult.success) {
                    throw new Error(launchResult?.error || 'Failed to launch browser');
                }
                browserContext = launchResult.context;
                shouldCleanupBrowser = true;
            }

            // Perform decoy actions if configured
            if (targetSettings && targetSettings.decoySettings && targetSettings.decoySettings.count > 0) {
                console.log('🎭 [DEBUG] Performing decoy actions...');
                await this.performDecoyActions(profile.id, targetSettings.decoySettings);
            }

            // Perform actual comment automation
            console.log('💬 [DEBUG] Starting comment automation...');
            const result = await this.performCommentActions(profile.id, targetSettings);

            // Cleanup only if we launched a new browser
            if (shouldCleanupBrowser) {
                console.log('🧹 [DEBUG] Cleaning up automation browser');
                const activeWindows = this.browserWindowManager.getActiveWindows();
                const window = activeWindows.find(w => w.profileId === profile.id);
                if (window) {
                    await this.browserWindowManager.closeWindow(window.windowId);
                }
            } else {
                console.log('🔄 [DEBUG] Keeping existing browser open');
            }

            return result;
        } catch (error) {
            console.error('❌ [DEBUG] Comment automation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Run Share Timeline automation
     * @param {object} options - Share Timeline automation options
     * @returns {Promise<object>} Automation result
     */
    async runShareTimelineAutomation(options = {}) {
        try {
            console.log('🚀 Starting Share Timeline automation...');

            const ShareTimelineAutomation = require('../automation/ShareTimelineAutomation');
            const shareBot = new ShareTimelineAutomation(this.dbManager);

            // Get targeted profiles based on automation settings
            const profiles = await this.profileManager.getTargetedProfiles();
            if (profiles.length === 0) {
                throw new Error('No available profiles for automation based on target settings');
            }

            console.log(`📋 Found ${profiles.length} targeted profiles for Share Timeline automation`);
            const profile = profiles[0];

            // Initialize and run
            const initResult = await shareBot.initialize(profile.id);
            if (!initResult.success) {
                throw new Error(initResult.error);
            }

            const result = await shareBot.run(options);

            // Cleanup
            await shareBot.cleanup();

            return result;
        } catch (error) {
            console.error('Share Timeline automation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Run Share Groups automation
     * @param {object} options - Share Groups automation options
     * @returns {Promise<object>} Automation result
     */
    async runShareGroupsAutomation(options = {}) {
        try {
            console.log('🚀 [DEBUG] Starting Share Groups automation...');
            console.log('🚀 [DEBUG] Share Groups automation options received:', options);

            // Use target settings from options if provided, otherwise get from database
            const targetSettings = options.targetSettings || null;
            console.log('🎯 [DEBUG] Target settings for Share Groups automation:', targetSettings);

            // Get targeted profiles based on automation settings
            const profiles = await this.profileManager.getTargetedProfiles(targetSettings);
            console.log(`📊 [DEBUG] getTargetedProfiles returned ${profiles.length} profiles`);

            if (profiles.length === 0) {
                console.log('❌ [DEBUG] No profiles found for Share Groups automation');
                throw new Error('No available profiles for automation based on target settings');
            }

            console.log(`📋 Found ${profiles.length} targeted profiles for Share Groups automation`);
            console.log('📋 [DEBUG] Profile details:', profiles.map(p => ({ id: p.id, name: p.name })));

            const profile = profiles[0];
            console.log(`🎯 [DEBUG] Using profile for Share Groups automation: ${profile.name} (${profile.id})`);

            // Check if profile browser is already open in BrowserWindowManager
            let browserContext = null;
            let shouldCleanupBrowser = false;

            // Try to get existing browser context from BrowserWindowManager
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const existingWindow = activeWindows.find(w => w.profileId === profile.id);

            if (existingWindow) {
                // Use existing browser context
                console.log('🔄 [DEBUG] Using existing browser context for profile:', profile.id);
                browserContext = this.browserWindowManager.getWindowContext(existingWindow.windowId);
                if (!browserContext) {
                    console.log('⚠️ [DEBUG] Existing browser context not found, launching new one');
                    const launchResult = await this.launchBrowserForProfile(profile);
                    if (!launchResult || !launchResult.success) {
                        throw new Error(launchResult?.error || 'Failed to launch browser');
                    }
                    browserContext = launchResult.context;
                    shouldCleanupBrowser = true;
                }
            } else {
                // Launch new browser for automation
                console.log('🚀 [DEBUG] Launching new browser for automation');
                const launchResult = await this.launchBrowserForProfile(profile);
                if (!launchResult || !launchResult.success) {
                    throw new Error(launchResult?.error || 'Failed to launch browser');
                }
                browserContext = launchResult.context;
                shouldCleanupBrowser = true;
            }

            // Perform decoy actions if configured
            if (targetSettings && targetSettings.decoySettings && targetSettings.decoySettings.count > 0) {
                console.log('🎭 [DEBUG] Performing decoy actions...');
                await this.performDecoyActions(profile.id, targetSettings.decoySettings);
            }

            // Perform actual share groups automation
            console.log('📤 [DEBUG] Starting share groups automation...');
            const result = await this.performShareGroupsActions(profile.id, targetSettings);

            // Cleanup only if we launched a new browser
            if (shouldCleanupBrowser) {
                console.log('🧹 [DEBUG] Cleaning up automation browser');
                const activeWindows = this.browserWindowManager.getActiveWindows();
                const window = activeWindows.find(w => w.profileId === profile.id);
                if (window) {
                    await this.browserWindowManager.closeWindow(window.windowId);
                }
            } else {
                console.log('🔄 [DEBUG] Keeping existing browser open');
            }

            return result;
        } catch (error) {
            console.error('❌ [DEBUG] Share Groups automation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Perform decoy actions for anti-detection
     * @param {string} profileId - Profile ID
     * @param {object} decoySettings - Decoy settings
     * @returns {Promise<void>}
     */
    async performDecoyActions(profileId, decoySettings) {
        try {
            console.log(`🎭 Performing ${decoySettings.count} decoy actions...`);

            // Get decoy links from database
            const decoyLinks = await this.dbManager.getDecoyLinks();
            if (decoyLinks.length === 0) {
                console.log('⚠️ No decoy links configured, skipping decoy actions');
                return;
            }

            const activeDecoyLinks = decoyLinks.filter(link => link.active !== false);
            if (activeDecoyLinks.length === 0) {
                console.log('⚠️ No active decoy links found, skipping decoy actions');
                return;
            }

            for (let i = 0; i < decoySettings.count; i++) {
                // Select random decoy link
                const randomLink = activeDecoyLinks[Math.floor(Math.random() * activeDecoyLinks.length)];

                console.log(`🎭 Decoy action ${i + 1}/${decoySettings.count}: Visiting ${randomLink.title || randomLink.url}`);

                // Navigate to decoy link
                if (this.browserConnector) {
                    await this.browserConnector.navigateToUrl(profileId, randomLink.url);
                } else {
                    // Use BrowserWindowManager if available
                    const activeWindows = this.browserWindowManager.getActiveWindows();
                    const window = activeWindows.find(w => w.profileId === profileId);
                    if (window) {
                        await this.browserWindowManager.navigateWindow(window.windowId, randomLink.url);
                    }
                }

                // Wait on the page for the specified delay
                await new Promise(resolve => setTimeout(resolve, decoySettings.delay));

                console.log(`✅ Decoy action ${i + 1} completed`);
            }

            console.log('🎭 All decoy actions completed');
        } catch (error) {
            console.error('❌ Decoy actions failed:', error);
            // Don't throw error, just log it as decoy actions are optional
        }
    }

    /**
     * Perform actual comment actions
     * @param {string} profileId - Profile ID
     * @param {object} targetSettings - Target settings
     * @returns {Promise<object>} Comment result
     */
    async performCommentActions(profileId, targetSettings) {
        try {
            console.log('💬 Starting comment actions...');

            // Get automation settings
            const automationSettings = await this.dbManager.getAutomationSettings();
            const commentSettings = automationSettings.comment || { enabled: true, delay: 3, maxPerSession: 20 };

            if (!commentSettings.enabled) {
                return { success: false, error: 'Comment automation is disabled in settings' };
            }

            // Get comments from database
            const comments = await this.dbManager.getComments();
            const activeComments = comments.filter(comment => comment.active !== false);

            if (activeComments.length === 0) {
                return { success: false, error: 'No active comments found in database' };
            }

            let commentsPosted = 0;
            const maxComments = commentSettings.maxPerSession === 0 ? 999999 : commentSettings.maxPerSession;

            console.log(`💬 Will post up to ${maxComments} comments with ${commentSettings.delay}s delay`);
            console.log(`💬 Available comments: ${activeComments.length}`);

            // Navigate to Facebook feed
            if (this.browserConnector) {
                await this.browserConnector.navigateToUrl(profileId, 'https://facebook.com');
            }

            // Wait for page to load
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Perform comment actions
            for (let i = 0; i < maxComments; i++) {
                try {
                    // Find comment boxes using Facebook's actual selectors
                    const commentSelectors = [
                        'div[aria-label="Write a comment…"][contenteditable="true"]',
                        'div[aria-placeholder="Write a comment…"]',
                        'div.xzsf02u.x1a2a7pz.x1n2onr6.x14wi4xw[contenteditable="true"]',
                        'div[data-lexical-editor="true"][role="textbox"]',
                        '[aria-label*="Write a comment"]'
                    ];

                    let commentPosted = false;

                    for (const selector of commentSelectors) {
                        if (this.browserConnector) {
                            // Check if comment box exists
                            const waitResult = await this.browserConnector.waitForElement(profileId, selector, 2000);
                            if (waitResult.success && waitResult.result?.found) {
                                // Select random comment
                                const randomComment = activeComments[Math.floor(Math.random() * activeComments.length)];

                                // Type comment
                                const typeResult = await this.browserConnector.typeText(profileId, selector, randomComment.text);
                                if (typeResult.success) {
                                    // Wait a bit
                                    await new Promise(resolve => setTimeout(resolve, 1000));

                                    // Try to find and click submit button (usually Enter key or specific button)
                                    // For Facebook comments, usually pressing Enter submits the comment
                                    // Let's try pressing Enter first, then look for submit buttons
                                    const submitResult = await this.browserConnector.executeScript(profileId, `
                                        const activeElement = document.activeElement;
                                        if (activeElement && activeElement.getAttribute('contenteditable') === 'true') {
                                            const enterEvent = new KeyboardEvent('keydown', {
                                                key: 'Enter',
                                                code: 'Enter',
                                                keyCode: 13,
                                                which: 13,
                                                bubbles: true
                                            });
                                            activeElement.dispatchEvent(enterEvent);
                                            return true;
                                        }
                                        return false;
                                    `);

                                    if (submitResult.success && submitResult.result) {
                                        commentPosted = true;
                                        break;
                                    }

                                    // If Enter didn't work, try to find submit buttons
                                    const submitSelectors = [
                                        'div[role="button"]:has-text("Post")',
                                        'button[type="submit"]',
                                        '[aria-label="Post"]',
                                        'div[data-testid*="comment-submit"]'
                                    ];

                                    for (const submitSelector of submitSelectors) {
                                        const submitResult = await this.browserConnector.clickElement(profileId, submitSelector);
                                        if (submitResult.success && submitResult.result?.found) {
                                            commentPosted = true;
                                            break;
                                        }
                                    }

                                    if (commentPosted) break;
                                }
                            }
                        }
                    }

                    if (commentPosted) {
                        commentsPosted++;
                        console.log(`💬 Comment ${commentsPosted}/${maxComments} posted`);

                        // Wait between comments
                        await new Promise(resolve => setTimeout(resolve, commentSettings.delay * 1000));

                        // Scroll down to find more posts
                        if (this.browserConnector) {
                            await this.browserConnector.executeScript(profileId, 'window.scrollBy(0, 400)');
                        }

                        await new Promise(resolve => setTimeout(resolve, 1500));
                    } else {
                        console.log('⚠️ No more comment boxes found, ending comment automation');
                        break;
                    }
                } catch (error) {
                    console.error(`❌ Error posting comment ${i + 1}:`, error);
                    continue;
                }
            }

            console.log(`✅ Comment automation completed. Posted ${commentsPosted} comments`);

            return {
                success: true,
                commentsPosted,
                message: `Successfully posted ${commentsPosted} comments`
            };

        } catch (error) {
            console.error('❌ Comment actions failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Perform actual share to groups actions
     * @param {string} profileId - Profile ID
     * @param {object} targetSettings - Target settings
     * @returns {Promise<object>} Share result
     */
    async performShareGroupsActions(profileId, targetSettings) {
        try {
            console.log('📤 Starting share to groups actions...');

            // Get automation settings
            const automationSettings = await this.dbManager.getAutomationSettings();
            const shareGroupsSettings = automationSettings.shareGroups || { enabled: true, delay: 5, maxPerSession: 10 };

            if (!shareGroupsSettings.enabled) {
                return { success: false, error: 'Share Groups automation is disabled in settings' };
            }

            // Get group links and target links from database
            const groupLinks = await this.dbManager.getGroupLinks();
            const activeGroupLinks = groupLinks.filter(link => link.active !== false);

            const targetLinks = await this.dbManager.getLinks();
            const activeTargetLinks = targetLinks.filter(link => link.active !== false);

            const comments = await this.dbManager.getComments();
            const activeComments = comments.filter(comment => comment.active !== false);

            if (activeGroupLinks.length === 0) {
                return { success: false, error: 'No active group links found in database' };
            }

            if (activeTargetLinks.length === 0) {
                return { success: false, error: 'No active target links found in database' };
            }

            let sharesPosted = 0;
            const maxShares = shareGroupsSettings.maxPerSession === 0 ? 999999 : shareGroupsSettings.maxPerSession;

            console.log(`📤 Will share to up to ${maxShares} groups with ${shareGroupsSettings.delay}s delay`);
            console.log(`📤 Available groups: ${activeGroupLinks.length}, target links: ${activeTargetLinks.length}`);

            // Perform share to groups actions
            for (let i = 0; i < maxShares && i < activeGroupLinks.length; i++) {
                try {
                    const groupLink = activeGroupLinks[i];
                    const randomTargetLink = activeTargetLinks[Math.floor(Math.random() * activeTargetLinks.length)];
                    const randomComment = activeComments.length > 0 ?
                        activeComments[Math.floor(Math.random() * activeComments.length)] : null;

                    console.log(`📤 Share ${i + 1}/${maxShares}: Posting to group ${groupLink.title || groupLink.url}`);

                    // Navigate to group
                    if (this.browserConnector) {
                        await this.browserConnector.navigateToUrl(profileId, groupLink.url);
                    }

                    // Wait for page to load
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    // Find and click "Write something..." button to start posting
                    const writeSelectors = [
                        'div[data-mcomponent="ServerTextArea"]:has-text("Write something...")',
                        'div[aria-label*="create a post"]',
                        'div[role="button"]:has-text("Write something")',
                        'div.native-text:has-text("Write something...")'
                    ];

                    let postStarted = false;
                    for (const selector of writeSelectors) {
                        if (this.browserConnector) {
                            const clickResult = await this.browserConnector.clickElement(profileId, selector);
                            if (clickResult.success && clickResult.result?.found) {
                                postStarted = true;
                                break;
                            }
                        }
                    }

                    if (!postStarted) {
                        console.log('⚠️ Could not find post creation button, skipping this group');
                        continue;
                    }

                    // Wait for post editor to open
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    // Find the main text area and type content
                    const textAreaSelectors = [
                        'div[data-mcomponent="ServerTextArea"][data-type="text"]',
                        'div[role="button"][aria-label*="create a post"]',
                        'div.native-text:has-text("Write something")'
                    ];

                    let contentAdded = false;
                    for (const selector of textAreaSelectors) {
                        if (this.browserConnector) {
                            // Create post content
                            let postContent = randomTargetLink.url;
                            if (randomComment && randomComment.text) {
                                postContent = `${randomComment.text}\n\n${randomTargetLink.url}`;
                            }

                            const typeResult = await this.browserConnector.typeText(profileId, selector, postContent);
                            if (typeResult.success) {
                                contentAdded = true;
                                break;
                            }
                        }
                    }

                    if (!contentAdded) {
                        console.log('⚠️ Could not add content to post, skipping this group');
                        continue;
                    }

                    // Wait a bit before posting
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    // Find and click POST button
                    const postButtonSelectors = [
                        'div[data-mcomponent="ServerTextArea"]:has-text("POST")',
                        'div[role="button"]:has-text("POST")',
                        'span.f2:has-text("POST")',
                        'div.native-text:has-text("POST")'
                    ];

                    let posted = false;
                    for (const selector of postButtonSelectors) {
                        if (this.browserConnector) {
                            const clickResult = await this.browserConnector.clickElement(profileId, selector);
                            if (clickResult.success && clickResult.result?.found) {
                                posted = true;
                                break;
                            }
                        }
                    }

                    if (posted) {
                        sharesPosted++;
                        console.log(`📤 Share ${sharesPosted}/${maxShares} posted successfully`);

                        // Wait between shares
                        await new Promise(resolve => setTimeout(resolve, shareGroupsSettings.delay * 1000));
                    } else {
                        console.log('⚠️ Could not find POST button, share may not have been posted');
                    }

                } catch (error) {
                    console.error(`❌ Error sharing to group ${i + 1}:`, error);
                    continue;
                }
            }

            console.log(`✅ Share to groups automation completed. Posted ${sharesPosted} shares`);

            return {
                success: true,
                sharesPosted,
                message: `Successfully shared to ${sharesPosted} groups`
            };

        } catch (error) {
            console.error('❌ Share to groups actions failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Perform actual like actions
     * @param {string} profileId - Profile ID
     * @param {object} targetSettings - Target settings
     * @returns {Promise<object>} Like result
     */
    async performLikeActions(profileId, targetSettings) {
        try {
            console.log('❤️ Starting like actions...');

            // Get automation settings
            const automationSettings = await this.dbManager.getAutomationSettings();
            const likeSettings = automationSettings.like || { enabled: true, delay: 2, maxPerSession: 50 };

            if (!likeSettings.enabled) {
                return { success: false, error: 'Like automation is disabled in settings' };
            }

            let likesPerformed = 0;
            const maxLikes = likeSettings.maxPerSession === 0 ? 999999 : likeSettings.maxPerSession;

            console.log(`❤️ Will perform up to ${maxLikes} likes with ${likeSettings.delay}s delay`);

            // Navigate to Facebook feed
            if (this.browserConnector) {
                await this.browserConnector.navigateToUrl(profileId, 'https://facebook.com');
            }

            // Wait for page to load
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Perform like actions
            for (let i = 0; i < maxLikes; i++) {
                try {
                    // Find like buttons using Facebook's actual selectors
                    const likeSelectors = [
                        'div[aria-label="Like"][role="button"]',
                        'div[aria-label="Like"].x1i10hfl',
                        'span[data-ad-rendering-role="like_button"]',
                        'div[role="button"]:has(span[data-ad-rendering-role="like_button"])',
                        '[aria-label="Like"]'
                    ];

                    let likeClicked = false;

                    for (const selector of likeSelectors) {
                        if (this.browserConnector) {
                            const clickResult = await this.browserConnector.clickElement(profileId, selector);
                            if (clickResult.success && clickResult.result?.found) {
                                likeClicked = true;
                                break;
                            }
                        }
                    }

                    if (likeClicked) {
                        likesPerformed++;
                        console.log(`❤️ Like ${likesPerformed}/${maxLikes} performed`);

                        // Wait between likes
                        await new Promise(resolve => setTimeout(resolve, likeSettings.delay * 1000));

                        // Scroll down to find more posts
                        if (this.browserConnector) {
                            await this.browserConnector.executeScript(profileId, 'window.scrollBy(0, 300)');
                        }

                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } else {
                        console.log('⚠️ No more like buttons found, ending like automation');
                        break;
                    }
                } catch (error) {
                    console.error(`❌ Error performing like ${i + 1}:`, error);
                    continue;
                }
            }

            console.log(`✅ Like automation completed. Performed ${likesPerformed} likes`);

            return {
                success: true,
                likesPerformed,
                message: `Successfully performed ${likesPerformed} likes`
            };

        } catch (error) {
            console.error('❌ Like actions failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Cleanup all automations and campaigns
     * @returns {Promise<void>}
     */
    async cleanup() {
        console.log('🧹 Cleaning up automation controller...');

        // Stop all campaigns
        await this.stopAllCampaigns();

        // Cleanup Facebook automation
        await this.facebookAutomation.cleanup();

        console.log('✅ Automation controller cleanup complete');
    }
}

module.exports = AutomationController;
