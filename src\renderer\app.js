// Main application JavaScript for FaceBot Multi
class FaceBotApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.profiles = [];
        this.campaigns = [];
        this.proxies = [];
        this.logs = [];
        this.settings = {};
        this.categories = [];
        this.selectedCategory = 'all';

        this.init();
    }

    async init() {
        try {
            // Initialize UI
            this.setupEventListeners();
            this.updateClock();
            this.showLoading('Initializing application...');

            // Load initial data
            await this.loadInitialData();

            // Setup real-time updates
            this.setupRealTimeUpdates();

            this.hideLoading();
            this.showToast('Application initialized successfully', 'success');

            // Update init time
            document.getElementById('init-time').textContent = new Date().toLocaleTimeString();
        } catch (error) {
            this.hideLoading();
            this.showToast(`Initialization failed: ${error.message}`, 'error');
            console.error('App initialization failed:', error);
        }
    }

    setupEventListeners() {
        // Sidebar navigation
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.navigateToPage(page);
            });
        });

        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.refreshCurrentPage();
        });

        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'r':
                        e.preventDefault();
                        this.refreshCurrentPage();
                        break;
                    case '1':
                        e.preventDefault();
                        this.navigateToPage('dashboard');
                        break;
                    case '2':
                        e.preventDefault();
                        this.navigateToPage('profiles');
                        break;
                    case '3':
                        e.preventDefault();
                        this.navigateToPage('automation');
                        break;
                }
            }
        });
    }

    async loadInitialData() {
        try {
            // Load profiles
            this.profiles = await window.electronAPI.profiles.getAll();

            // Load categories
            this.categories = await window.electronAPI.categories.getAll();

            // Load proxies
            this.proxies = await window.electronAPI.proxies.getAll();

            // Load recent logs
            this.logs = await window.electronAPI.logs.getAll({ limit: 50 });

            // Load settings
            this.settings = await window.electronAPI.settings.get();

            // Update dashboard stats
            this.updateDashboardStats();

            // Update red list count
            await this.updateRedListCount();

        } catch (error) {
            console.error('Failed to load initial data:', error);
            throw error;
        }
    }

    setupRealTimeUpdates() {
        // Listen for automation progress updates
        window.electronAPI.automation.onProgress((event, data) => {
            this.handleAutomationProgress(data);
        });

        // Listen for automation status changes
        window.electronAPI.automation.onStatusChange((event, data) => {
            this.handleAutomationStatusChange(data);
        });

        // Listen for new logs
        window.electronAPI.logs.onNewLog((event, log) => {
            this.handleNewLog(log);
        });
    }

    navigateToPage(page) {
        // Update active sidebar item
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).classList.add('active');

        // Update page title and content
        this.currentPage = page;
        this.loadPageContent(page);
    }

    async loadPageContent(page) {
        const pageTitle = document.getElementById('page-title');
        const pageSubtitle = document.getElementById('page-subtitle');
        const pageContent = document.getElementById('page-content');

        // Add fade out effect
        pageContent.style.opacity = '0';

        setTimeout(async () => {
            switch (page) {
                case 'dashboard':
                    pageTitle.textContent = 'Dashboard';
                    pageSubtitle.textContent = 'Overview of your automation activities';
                    await this.loadDashboard();
                    break;

                case 'profiles':
                    pageTitle.textContent = 'Profiles';
                    pageSubtitle.textContent = 'Manage your Facebook profiles';
                    await this.loadProfiles();
                    break;

                case 'automation':
                    pageTitle.textContent = 'Automation';
                    pageSubtitle.textContent = 'Create and manage automation campaigns';
                    await this.loadAutomation();
                    break;

                case 'scheduling':
                    pageTitle.textContent = 'Scheduling';
                    pageSubtitle.textContent = 'Configure automation schedules and timing';
                    await this.loadScheduling();
                    break;

                case 'proxies':
                    pageTitle.textContent = 'Proxies';
                    pageSubtitle.textContent = 'Manage proxy servers';
                    await this.loadProxies();
                    break;

                case 'logs':
                    pageTitle.textContent = 'Logs';
                    pageSubtitle.textContent = 'View automation activity logs';
                    await this.loadLogs();
                    break;

                case 'settings':
                    pageTitle.textContent = 'Settings';
                    pageSubtitle.textContent = 'Configure automation and scheduling settings';
                    await this.loadSettings();
                    break;
            }

            // Add fade in effect
            pageContent.style.opacity = '1';
            pageContent.classList.add('fade-in');
        }, 150);
    }

    async loadDashboard() {
        const content = `
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Stats Cards -->
                <div class="card p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Profiles</p>
                            <p class="text-2xl font-semibold">${this.profiles.length}</p>
                        </div>
                    </div>
                </div>

                <div class="card p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                            <i class="fas fa-play"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Profiles</p>
                            <p class="text-2xl font-semibold">${this.profiles.length}</p>
                        </div>
                    </div>
                </div>

                <div class="card p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600 mr-4">
                            <i class="fas fa-check"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Success Rate</p>
                            <p class="text-2xl font-semibold">${this.calculateSuccessRate()}%</p>
                        </div>
                    </div>
                </div>

                <div class="card p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600 mr-4">
                            <i class="fas fa-network-wired"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Proxies</p>
                            <p class="text-2xl font-semibold">${this.proxies.length}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Quick Automation</h3>
                    <div class="space-y-3">
                        <button id="run-like-btn" class="bg-red-500 hover:bg-red-600 w-full text-white py-2 px-4 rounded-lg" onclick="app.runLikeAutomation()">
                            <i class="fas fa-heart mr-2"></i>Run Like
                        </button>
                        <button id="run-comment-btn" class="bg-blue-500 hover:bg-blue-600 w-full text-white py-2 px-4 rounded-lg" onclick="app.runCommentAutomation()">
                            <i class="fas fa-comment mr-2"></i>Run Comment
                        </button>
                        <button id="run-share-timeline-btn" class="bg-green-500 hover:bg-green-600 w-full text-white py-2 px-4 rounded-lg" onclick="app.runShareTimelineAutomation()">
                            <i class="fas fa-share mr-2"></i>Run Share Timeline
                        </button>
                        <button id="run-share-groups-btn" class="bg-purple-500 hover:bg-purple-600 w-full text-white py-2 px-4 rounded-lg" onclick="app.runShareGroupsAutomation()">
                            <i class="fas fa-share-alt mr-2"></i>Run Share Groups
                        </button>
                        <button class="bg-orange-500 hover:bg-orange-600 w-full text-white py-2 px-4 rounded-lg" onclick="app.forceCleanupProfiles()">
                            <i class="fas fa-broom mr-2"></i>Force Cleanup
                        </button>
                    </div>
                </div>

                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">System Status</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">Database</span>
                            <span class="flex items-center text-green-600">
                                <span class="status-indicator status-active"></span>
                                Connected
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">Profiles Directory</span>
                            <span class="flex items-center text-green-600">
                                <span class="status-indicator status-active"></span>
                                Ready
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">Automation Engine</span>
                            <span class="flex items-center text-green-600">
                                <span class="status-indicator status-active"></span>
                                Ready
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
                <div class="space-y-3">
                    ${this.renderRecentLogs()}
                </div>
            </div>
        `;

        document.getElementById('page-content').innerHTML = content;

        // Load current settings and update Quick Actions buttons
        await this.loadCurrentSettings();
        this.updateQuickActionsButtons();
    }

    async loadProfiles() {
        const content = `
            <div class="mb-6 flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold">Manage Profiles</h3>
                    <p class="text-gray-600">Create and manage your Facebook automation profiles</p>
                </div>
                <div class="space-x-2">
                    <button class="btn-primary text-white py-2 px-4 rounded-lg" onclick="app.showCreateProfileModal()">
                        <i class="fas fa-plus mr-2"></i>Add Profile
                    </button>
                    <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg" onclick="app.showCreateCategoryModal()">
                        <i class="fas fa-tags mr-2"></i>Add Category
                    </button>
                    <button class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg" onclick="app.clearAllProfiles()">
                        <i class="fas fa-trash-alt mr-2"></i>Clear All
                    </button>
                    <div class="relative">
                        <button class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg" onclick="app.toggleImportExportMenu()">
                            <i class="fas fa-exchange-alt mr-2"></i>Import/Export
                        </button>
                        <div id="import-export-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                            <div class="py-1">
                                <button class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="app.exportProfilesToRAR()">
                                    <i class="fas fa-download mr-2"></i>Export to RAR
                                </button>
                                <button class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="app.importProfilesFromRAR()">
                                    <i class="fas fa-upload mr-2"></i>Import from RAR
                                </button>
                                <hr class="my-1">
                                <button class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="app.importFromFacebot()">
                                    <i class="fas fa-file-import mr-2"></i>Import from FaceBot
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Category Filter -->
            <div class="mb-6">
                <div class="flex flex-wrap gap-2">
                    <button class="category-filter ${this.selectedCategory === 'all' ? 'active' : ''}"
                            onclick="app.filterByCategory('all')">
                        <i class="fas fa-list mr-1"></i>All (${this.profiles.length})
                    </button>
                    ${this.renderCategoryFilters()}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                ${this.renderProfiles()}
            </div>
        `;

        document.getElementById('page-content').innerHTML = content;
    }

    renderProfiles() {
        // Filter profiles by selected category
        let filteredProfiles = this.profiles;
        if (this.selectedCategory !== 'all') {
            filteredProfiles = this.profiles.filter(profile => profile.category === this.selectedCategory);
        }

        if (filteredProfiles.length === 0) {
            return `
                <div class="col-span-full text-center py-12">
                    <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-600 mb-2">No profiles found</h3>
                    <p class="text-gray-500 mb-4">${this.selectedCategory === 'all' ? 'Create your first profile to get started' : 'No profiles in this category'}</p>
                    <button class="btn-primary text-white py-2 px-4 rounded-lg" onclick="app.showCreateProfileModal()">
                        <i class="fas fa-plus mr-2"></i>Add Profile
                    </button>
                </div>
            `;
        }

        return filteredProfiles.map(profile => {
            const category = this.categories.find(c => c.id === profile.category);
            const categoryName = category ? category.name : 'General';
            const categoryColor = category ? category.color : '#6c757d';

            return `
            <div class="card p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h4 class="font-semibold">${profile.name}</h4>
                        <span class="inline-block px-2 py-1 text-xs rounded-full text-white mt-1"
                              style="background-color: ${categoryColor}">
                            ${categoryName}
                        </span>
                    </div>

                </div>
                <div class="space-y-2 text-sm text-gray-600">
                    <div><strong>Type:</strong> ${profile.type || 'Standard'}</div>
                    <div><strong>Email:</strong> ${profile.email || 'Not set'}</div>
                    <div><strong>Last Used:</strong> ${profile.lastUsed ? new Date(profile.lastUsed).toLocaleDateString() : 'Never'}</div>

                </div>
                <div class="mt-4 flex flex-wrap gap-2">
                    <button class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm" onclick="app.testProfile('${profile.id}')">
                        <i class="fas fa-vial mr-1"></i>Test
                    </button>
                    <button class="bg-green-500 hover:bg-green-600 text-white py-1 px-3 rounded text-sm" onclick="app.launchBrowser('${profile.id}')">
                        <i class="fas fa-globe mr-1"></i>Launch
                    </button>
                    <button class="bg-gray-500 hover:bg-gray-600 text-white py-1 px-3 rounded text-sm" onclick="app.editProfile('${profile.id}')">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </button>
                    <button class="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded text-sm" onclick="app.deleteProfile('${profile.id}')">
                        <i class="fas fa-trash mr-1"></i>Delete
                    </button>
                </div>
            </div>
        `}).join('');
    }

    renderRecentLogs() {
        const recentLogs = this.logs.slice(0, 5);
        if (recentLogs.length === 0) {
            return '<p class="text-gray-500 text-center py-4">No recent activity</p>';
        }

        return recentLogs.map(log => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <span class="status-indicator ${log.status === 'success' ? 'status-active' : 'status-inactive'}"></span>
                    <span class="text-sm">${log.action}: ${log.message}</span>
                </div>
                <span class="text-xs text-gray-500">${new Date(log.timestamp).toLocaleTimeString()}</span>
            </div>
        `).join('');
    }



    calculateSuccessRate() {
        if (this.logs.length === 0) return 0;
        const successCount = this.logs.filter(log => log.status === 'success').length;
        return Math.round((successCount / this.logs.length) * 100);
    }

    updateDashboardStats() {
        // Update sidebar stats
        document.getElementById('profile-count').textContent = this.profiles.length;
        document.getElementById('active-campaigns').textContent = this.campaigns.length;
    }

    updateClock() {
        const updateTime = () => {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        };

        updateTime();
        setInterval(updateTime, 1000);
    }

    async refreshCurrentPage() {
        this.showLoading('Refreshing...');
        try {
            await this.loadInitialData();
            await this.loadPageContent(this.currentPage);
            this.showToast('Page refreshed successfully', 'success');
        } catch (error) {
            this.showToast(`Refresh failed: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    showLoading(text = 'Loading...') {
        document.getElementById('loading-text').textContent = text;
        document.getElementById('loading-overlay').classList.remove('hidden');
    }

    hideLoading() {
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `bg-white border-l-4 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;

        const colors = {
            success: 'border-green-500',
            error: 'border-red-500',
            warning: 'border-yellow-500',
            info: 'border-blue-500'
        };

        const icons = {
            success: 'fas fa-check-circle text-green-500',
            error: 'fas fa-exclamation-circle text-red-500',
            warning: 'fas fa-exclamation-triangle text-yellow-500',
            info: 'fas fa-info-circle text-blue-500'
        };

        toast.classList.add(colors[type]);
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="${icons[type]} mr-3"></i>
                <span>${message}</span>
                <button class="ml-auto text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.getElementById('toast-container').appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 5000);
    }

    // Facebook Automation page
    async loadAutomation() {
        const content = `
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Quick Actions -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <button id="run-like-btn" class="bg-red-500 hover:bg-red-600 w-full text-white py-3 px-4 rounded-lg" onclick="app.runLikeAutomation()">
                            <i class="fas fa-heart mr-2"></i>Run Like Automation
                        </button>
                        <button id="run-comment-btn" class="bg-blue-500 hover:bg-blue-600 w-full text-white py-3 px-4 rounded-lg" onclick="app.runCommentAutomation()">
                            <i class="fas fa-comment mr-2"></i>Run Comment Automation
                        </button>
                        <button id="run-share-timeline-btn" class="bg-green-500 hover:bg-green-600 w-full text-white py-3 px-4 rounded-lg" onclick="app.runShareTimelineAutomation()">
                            <i class="fas fa-share mr-2"></i>Run Share Timeline
                        </button>
                        <button id="run-share-groups-btn" class="bg-purple-500 hover:bg-purple-600 w-full text-white py-3 px-4 rounded-lg" onclick="app.runShareGroupsAutomation()">
                            <i class="fas fa-share-alt mr-2"></i>Run Share Groups
                        </button>
                        <button class="bg-orange-500 hover:bg-orange-600 w-full text-white py-3 px-4 rounded-lg" onclick="app.showGroupSharingModal()">
                            <i class="fas fa-cog mr-2"></i>Advanced Group Sharing
                        </button>
                    </div>
                </div>

                <!-- Active Schedules -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Active Schedules</h3>
                    <div class="space-y-3">
                        ${this.renderActiveSchedules()}
                    </div>
                </div>
            </div>

            <!-- Quick Schedule Templates -->
            <div class="mt-6">
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Quick Schedule Templates</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <button class="bg-red-100 hover:bg-red-200 text-red-700 py-3 px-4 rounded-lg" onclick="app.createQuickSchedule('like_hourly')">
                            <i class="fas fa-heart mb-2"></i>
                            <div class="text-sm font-medium">Like Every Hour</div>
                        </button>
                        <button class="bg-blue-100 hover:bg-blue-200 text-blue-700 py-3 px-4 rounded-lg" onclick="app.createQuickSchedule('comment_daily')">
                            <i class="fas fa-comment mb-2"></i>
                            <div class="text-sm font-medium">Comment Daily</div>
                        </button>
                        <button class="bg-green-100 hover:bg-green-200 text-green-700 py-3 px-4 rounded-lg" onclick="app.createQuickSchedule('share_timeline_weekly')">
                            <i class="fas fa-share mb-2"></i>
                            <div class="text-sm font-medium">Share Weekly</div>
                        </button>
                        <button class="bg-purple-100 hover:bg-purple-200 text-purple-700 py-3 px-4 rounded-lg" onclick="app.createQuickSchedule('share_groups_custom')">
                            <i class="fas fa-share-alt mb-2"></i>
                            <div class="text-sm font-medium">Custom Groups</div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Active Automations -->
            <div class="mb-6">
                <h4 class="text-md font-semibold mb-3">Active Automations</h4>
                <div id="active-automations" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    ${this.renderActiveAutomations()}
                </div>
            </div>

            <!-- Scheduled Automations -->
            <div class="mb-6">
                <h4 class="text-md font-semibold mb-3">Scheduled Automations</h4>
                <div id="scheduled-automations" class="space-y-3">
                    ${this.renderScheduledAutomations()}
                </div>
            </div>

            <!-- Smart Selector Statistics -->
            <div class="mb-6">
                <h4 class="text-md font-semibold mb-3">Smart Selector Performance</h4>
                <div class="card p-4">
                    <div id="selector-stats">
                        ${this.renderSelectorStats()}
                    </div>
                </div>
            </div>
        `;

        document.getElementById('page-content').innerHTML = content;

        // Load automation data
        await this.loadAutomationData();

        // Load current settings and update Quick Actions buttons
        await this.loadCurrentSettings();
        this.updateQuickActionsButtons();
    }

    async loadProxies() {
        document.getElementById('page-content').innerHTML = '<div class="text-center py-12"><h3>Proxies page coming soon...</h3></div>';
    }

    async loadLogs() {
        document.getElementById('page-content').innerHTML = '<div class="text-center py-12"><h3>Logs page coming soon...</h3></div>';
    }

    async loadScheduling() {
        const content = `
            <div class="mb-6 flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold">Automation Scheduling</h3>
                    <p class="text-gray-600">Configure automation schedules and timing settings</p>
                </div>
                <div class="space-x-2">
                    <button class="btn-primary text-white py-2 px-4 rounded-lg" onclick="app.showCreateScheduleModal()">
                        <i class="fas fa-plus mr-2"></i>Create Schedule
                    </button>
                </div>
            </div>

            <!-- Quick Schedule Templates -->
            <div class="mb-6">
                <h4 class="text-md font-semibold mb-3">Quick Schedule Templates</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <button class="card p-4 text-left hover:bg-gray-50" onclick="app.createQuickSchedule('like_hourly')">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-heart text-red-500 mr-2"></i>
                            <span class="font-medium">Like Every Hour</span>
                        </div>
                        <p class="text-sm text-gray-600">Auto-like posts every hour</p>
                    </button>

                    <button class="card p-4 text-left hover:bg-gray-50" onclick="app.createQuickSchedule('comment_daily')">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-comment text-blue-500 mr-2"></i>
                            <span class="font-medium">Comment Daily</span>
                        </div>
                        <p class="text-sm text-gray-600">Auto-comment once per day</p>
                    </button>

                    <button class="card p-4 text-left hover:bg-gray-50" onclick="app.createQuickSchedule('share_timeline_weekly')">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-share text-green-500 mr-2"></i>
                            <span class="font-medium">Share Weekly</span>
                        </div>
                        <p class="text-sm text-gray-600">Share to timeline weekly</p>
                    </button>

                    <button class="card p-4 text-left hover:bg-gray-50" onclick="app.createQuickSchedule('share_groups_custom')">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-share-alt text-purple-500 mr-2"></i>
                            <span class="font-medium">Custom Groups</span>
                        </div>
                        <p class="text-sm text-gray-600">Custom group sharing schedule</p>
                    </button>
                </div>
            </div>

            <!-- Active Schedules -->
            <div class="mb-6">
                <h4 class="text-md font-semibold mb-3">Active Schedules</h4>
                <div id="active-schedules" class="space-y-3">
                    ${this.renderActiveSchedules()}
                </div>
            </div>

            <!-- Schedule Settings -->
            <div class="mb-6">
                <h4 class="text-md font-semibold mb-3">Global Schedule Settings</h4>
                <div class="card p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Default Delay Between Actions (seconds)
                            </label>
                            <input type="number" id="default-delay" class="w-full px-3 py-2 border border-gray-300 rounded-md"
                                   value="30" min="10" max="300">
                            <p class="text-xs text-gray-500 mt-1">Minimum delay between automation actions</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Max Concurrent Automations
                            </label>
                            <input type="number" id="max-concurrent" class="w-full px-3 py-2 border border-gray-300 rounded-md"
                                   value="3" min="1" max="10">
                            <p class="text-xs text-gray-500 mt-1">Maximum number of simultaneous automations</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Working Hours
                            </label>
                            <div class="flex space-x-2">
                                <input type="time" id="work-start" class="flex-1 px-3 py-2 border border-gray-300 rounded-md" value="09:00">
                                <span class="self-center">to</span>
                                <input type="time" id="work-end" class="flex-1 px-3 py-2 border border-gray-300 rounded-md" value="17:00">
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Automation will only run during these hours</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Weekend Mode
                            </label>
                            <select id="weekend-mode" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                                <option value="normal">Normal Schedule</option>
                                <option value="reduced">Reduced Activity</option>
                                <option value="disabled">Disabled</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">How to handle weekend automation</p>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button class="btn-primary text-white py-2 px-4 rounded-lg" onclick="app.saveScheduleSettings()">
                            <i class="fas fa-save mr-2"></i>Save Settings
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('page-content').innerHTML = content;
        await this.loadScheduleData();
    }

    async loadSettings() {
        console.log('🔄 Loading settings page...');
        // Always reload settings from backend to ensure fresh data
        await this.loadCurrentSettings();

        const content = `
            <div class="space-y-6">
                <!-- Export/Import Settings -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Backup & Restore</h3>
                    <p class="text-gray-600 mb-4">Export your settings, profiles, and content to backup file or import from existing backup.</p>
                    <div class="flex flex-wrap gap-3">
                        <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg" onclick="app.exportSettings()">
                            <i class="fas fa-download mr-2"></i>Export Settings
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg" onclick="app.importSettings()">
                            <i class="fas fa-upload mr-2"></i>Import Settings
                        </button>
                    </div>
                </div>

                <!-- Target Selection Settings -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Automation Target Settings</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-3">Run Automation For</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="automation-target" value="all" class="mr-2" checked onchange="app.updateTargetSelection()">
                                    <span class="text-sm">All Profiles</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="automation-target" value="category" class="mr-2" onchange="app.updateTargetSelection()">
                                    <span class="text-sm">Specific Category</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="automation-target" value="profile" class="mr-2" onchange="app.updateTargetSelection()">
                                    <span class="text-sm">Specific Profile</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="automation-target" value="redlist" class="mr-2" onchange="app.updateTargetSelection()">
                                    <span class="text-sm">Red List Only</span>
                                </label>
                            </div>
                        </div>
                        <div id="target-selection-container" class="hidden">
                            <label class="block text-sm font-medium mb-2" id="target-selection-label">Select Target</label>
                            <select id="target-selection" class="w-full p-2 border rounded-lg">
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Save All Settings Button -->
                <div class="card p-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                    <div class="text-center">
                        <h3 class="text-xl font-bold mb-2">Save All Settings</h3>
                        <p class="mb-4 opacity-90">Save all automation, browser, schedule, and target settings at once</p>
                        <button class="bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-colors" onclick="app.saveAllSettings()">
                            <i class="fas fa-save mr-2"></i>Save All Settings
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('page-content').innerHTML = content;

        // Wait for DOM to be ready
        await new Promise(resolve => setTimeout(resolve, 100));

        // Load current settings from backend first
        await this.loadCurrentSettings();

        // Load content counts
        await this.updateContentCounts();

        // Populate forms with current settings
        await this.populateSettingsForms();

        // Add event listeners for target automation radio buttons
        document.querySelectorAll('input[name="automation-target"]').forEach(radio => {
            radio.addEventListener('change', () => {
                console.log('Radio button changed:', radio.value);
                this.updateTargetSelection();
            });
        });

        // Wait a bit more for DOM to be fully ready, then update target selection
        await new Promise(resolve => setTimeout(resolve, 200));
        await this.updateTargetSelection();
    }

    showCreateProfileModal() {
        this.showProfileModal(null); // null means create new profile
    }

    showProfileModal(profile = null) {
        const isEdit = profile !== null;
        const modalTitle = isEdit ? 'Edit Profile' : 'Create New Profile';
        const submitText = isEdit ? 'Update Profile' : 'Create Profile';

        const modalHTML = `
            <div class="modal-header">
                <h3 class="text-lg font-semibold">${modalTitle}</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

                    <form id="profile-form" onsubmit="app.handleProfileSubmit(event)" class="flex flex-col h-full">
                        <div class="space-y-4 overflow-y-auto flex-1 p-6">
                            <div>
                                <label for="profile-name" class="block text-sm font-medium text-gray-700 mb-1">
                                    Profile Name
                                </label>
                                <input
                                    type="text"
                                    id="profile-name"
                                    name="name"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="e.g., Profile 1"
                                    value="${profile?.name || ''}"
                                    required
                                />
                            </div>

                            <!-- Email and Password removed for Chrome profiles - Manual login supported -->

                            <div>
                                <label for="profile-type" class="block text-sm font-medium text-gray-700 mb-1">
                                    Profile Type
                                </label>
                                <select
                                    id="profile-type"
                                    name="type"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="chrome" ${profile?.type === 'chrome' ? 'selected' : ''}>Chrome Profile (Recommended)</option>
                                    <option value="standard" ${profile?.type === 'standard' ? 'selected' : ''}>Standard Profile</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">Chrome profiles support manual Google login</p>
                            </div>

                            <div>
                                <label for="profile-category" class="block text-sm font-medium text-gray-700 mb-1">
                                    Category
                                </label>
                                <select
                                    id="profile-category"
                                    name="category"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="">Select Category (Optional)</option>
                                    ${this.categories.map(cat => `
                                        <option value="${cat.id}" ${profile?.category === cat.id ? 'selected' : ''}>
                                            ${cat.name}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>

                            <div>
                                <label for="profile-notes" class="block text-sm font-medium text-gray-700 mb-1">
                                    Notes (Optional)
                                </label>
                                <textarea
                                    id="profile-notes"
                                    name="notes"
                                    rows="3"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="Optional notes about this profile..."
                                >${profile?.notes || ''}</textarea>
                            </div>


                        </div>

                        <div class="flex justify-end space-x-3 p-6 border-t flex-shrink-0">
                            <button
                                type="button"
                                onclick="app.closeModal()"
                                class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <i class="fas fa-save mr-2"></i>${submitText}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        // Store current editing profile
        this.editingProfile = profile;

        // Show modal using new system
        this.showModal(modalHTML, { preventClose: true });
    }

    async testProfile(profileId) {
        this.showLoading('Testing profile...');
        try {
            const result = await window.electronAPI.profiles.test(profileId);
            if (result.success) {
                this.showToast('Profile test successful', 'success');
            } else {
                this.showToast(`Profile test failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Profile test failed: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }



    async handleProfileSubmit(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const profileData = {
            name: formData.get('name'),
            type: formData.get('type') || 'chrome',
            category: formData.get('category') || 'general',
            notes: formData.get('notes') || ''
        };

        // For Chrome profiles, no email/password needed (manual login)
        // Email and password will be handled manually in browser

        this.showLoading(this.editingProfile ? 'Updating profile...' : 'Creating profile...');

        try {
            console.log('💾 [SINGLE ACCESS] Saving profile...');
            console.log('💾 [SINGLE ACCESS] Profile data:', profileData);

            let result;
            if (this.editingProfile) {
                // Update existing profile
                console.log('💾 [SINGLE ACCESS] Updating existing profile:', this.editingProfile.id);
                result = await window.electronAPI.profiles.update(this.editingProfile.id, profileData);
            } else {
                // Create new profile
                console.log('💾 [SINGLE ACCESS] Creating new profile');
                result = await window.electronAPI.profiles.create(profileData);
            }

            console.log('💾 [SINGLE ACCESS] Save result:', result);

            if (result && result.success) {
                this.showToast(
                    this.editingProfile ? 'Profile updated successfully' : 'Profile created successfully',
                    'success'
                );
                this.closeModal();
                this.editingProfile = null;

                // Reload profiles from database (SINGLE ACCESS POINT)
                console.log('🔄 [SINGLE ACCESS] Reloading profiles from database...');
                await this.reloadProfilesFromDatabase();
            } else {
                const errorMsg = result?.error || 'Failed to save profile';
                this.showToast(errorMsg, 'error');
            }
        } catch (error) {
            console.error('❌ [SINGLE ACCESS] Error saving profile:', error);
            this.showToast(`Failed to save profile: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    editProfile(profileId) {
        const profile = this.profiles.find(p => p.id === profileId);
        if (profile) {
            this.showProfileModal(profile);
        } else {
            this.showToast('Profile not found', 'error');
        }
    }

    async deleteProfile(profileId) {
        if (confirm('Are you sure you want to delete this profile?')) {
            try {
                console.log('🗑️ [SINGLE ACCESS] Deleting profile:', profileId);
                const result = await window.electronAPI.profiles.delete(profileId);

                if (result && result.success) {
                    this.showToast('Profile deleted successfully', 'success');

                    // Reload profiles from database (SINGLE ACCESS POINT)
                    console.log('🔄 [SINGLE ACCESS] Reloading profiles after delete...');
                    await this.reloadProfilesFromDatabase();
                } else {
                    const errorMsg = result?.error || 'Failed to delete profile';
                    this.showToast(errorMsg, 'error');
                }
            } catch (error) {
                console.error('❌ [SINGLE ACCESS] Error deleting profile:', error);
                this.showToast(`Failed to delete profile: ${error.message}`, 'error');
            }
        }
    }

    // Update profiles display without full page reload
    updateProfilesDisplay() {
        if (this.currentPage === 'profiles') {
            // Find the profiles grid container and update it
            const profilesGrid = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-6');
            if (profilesGrid) {
                profilesGrid.innerHTML = this.renderProfiles();
            } else {
                // Fallback: reload the entire profiles page
                this.loadProfiles();
            }
        }
    }

    /**
     * Reload profiles from database (SINGLE ACCESS POINT)
     */
    async reloadProfilesFromDatabase() {
        try {
            console.log('🔄 [SINGLE ACCESS] Reloading profiles from database...');

            // Get fresh data from database
            this.profiles = await window.electronAPI.profiles.getAll();
            console.log(`🔄 [SINGLE ACCESS] Loaded ${this.profiles.length} profiles from database`);

            // Update UI
            this.updateDashboardStats();

            // Re-render profiles if on profiles page
            if (this.currentPage === 'profiles') {
                await this.loadProfiles();
            }

            console.log('✅ [SINGLE ACCESS] Profiles reloaded successfully');
        } catch (error) {
            console.error('❌ [SINGLE ACCESS] Error reloading profiles:', error);
            this.showToast(`Error reloading profiles: ${error.message}`, 'error');
        }
    }

    /**
     * Clear all profiles (SINGLE ACCESS POINT)
     */
    async clearAllProfiles() {
        if (confirm('⚠️ Are you sure you want to delete ALL profiles? This action cannot be undone!')) {
            try {
                console.log('🗑️ [SINGLE ACCESS] Clearing all profiles...');
                this.showLoading('Clearing all profiles...');

                const result = await window.electronAPI.profiles.clearAll();

                if (result && result.success) {
                    this.showToast(`Successfully cleared ${result.clearedCount || 0} profiles`, 'success');

                    // Reload profiles from database (SINGLE ACCESS POINT)
                    console.log('🔄 [SINGLE ACCESS] Reloading profiles after clear all...');
                    await this.reloadProfilesFromDatabase();
                } else {
                    const errorMsg = result?.error || 'Failed to clear profiles';
                    this.showToast(errorMsg, 'error');
                }
            } catch (error) {
                console.error('❌ [SINGLE ACCESS] Error clearing all profiles:', error);
                this.showToast(`Error clearing profiles: ${error.message}`, 'error');
            } finally {
                this.hideLoading();
            }
        }
    }

    // Ensure all inputs are clickable and focusable
    ensureInputsClickable() {
        setTimeout(() => {
            const allInputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], textarea, select');
            allInputs.forEach(input => {
                // Remove any readonly attributes
                input.removeAttribute('readonly');
                input.removeAttribute('disabled');

                // Ensure input is focusable
                if (input.tabIndex < 0) {
                    input.tabIndex = 0;
                }

                // Add event listeners to ensure input works
                input.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.focus();
                    this.removeAttribute('readonly');
                }, { once: true });
            });
            console.log('Ensured all inputs are clickable:', allInputs.length);
        }, 200);
    }

    async importFromFacebot() {
        try {
            // Select accounts file
            const accountsFile = await window.electronAPI.file.selectFile({
                title: 'Select accounts.txt file',
                filters: [
                    { name: 'Text Files', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!accountsFile || accountsFile.canceled) {
                return;
            }

            // Select comments file (optional)
            const commentsFile = await window.electronAPI.file.selectFile({
                title: 'Select comments.txt file (optional)',
                filters: [
                    { name: 'Text Files', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            // Select shares file (optional)
            const sharesFile = await window.electronAPI.file.selectFile({
                title: 'Select shares.txt file (optional)',
                filters: [
                    { name: 'Text Files', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            this.showLoading('Importing profiles from FaceBot...');

            const result = await window.electronAPI.migration.importFromFacebot(
                accountsFile.filePaths[0],
                commentsFile?.filePaths?.[0] || null,
                sharesFile?.filePaths?.[0] || null
            );

            if (result.success) {
                this.showToast(`Successfully imported ${result.imported} profiles`, 'success');
                await this.loadInitialData();
                if (this.currentPage === 'profiles') {
                    await this.loadProfiles();
                }
            } else {
                throw new Error(result.error || 'Import failed');
            }
        } catch (error) {
            this.showToast(`Import failed: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    handleAutomationProgress(data) {
        // Handle real-time automation progress updates
        console.log('Automation progress:', data);
    }

    handleAutomationStatusChange(data) {
        // Handle automation status changes
        console.log('Automation status change:', data);
    }

    handleNewLog(log) {
        // Handle new log entries
        this.logs.unshift(log);
        if (this.logs.length > 100) {
            this.logs = this.logs.slice(0, 100);
        }

        // Update UI if on dashboard or logs page
        if (this.currentPage === 'dashboard' || this.currentPage === 'logs') {
            this.refreshCurrentPage();
        }
    }

    async launchBrowser(profileId) {
        try {
            this.showLoading('Launching browser...');
            const result = await window.electronAPI.profiles.launchBrowser(profileId);

            if (result.success) {
                this.showToast(`Browser launched for ${result.profileType} profile`, 'success');
            } else {
                // If launch failed due to profile being active, try force cleanup and retry
                if (result.error && result.error.includes('already active')) {
                    console.log('Profile appears stuck, attempting force cleanup...');

                    this.showLoading('Cleaning up stuck profile...');
                    const cleanupResult = await window.electronAPI.browser.forceCleanupProfile(profileId);

                    if (cleanupResult.success) {
                        this.showLoading('Retrying browser launch...');
                        const retryResult = await window.electronAPI.profiles.launchBrowser(profileId);

                        if (retryResult.success) {
                            this.showToast(`Browser launched for ${retryResult.profileType} profile (after cleanup)`, 'success');
                        } else {
                            throw new Error(retryResult.error || 'Failed to launch browser after cleanup');
                        }
                    } else {
                        throw new Error('Failed to cleanup stuck profile');
                    }
                } else {
                    this.showToast(`Browser launch failed: ${result.error}`, 'error');
                }
            }
        } catch (error) {
            console.error('Browser launch failed:', error);
            this.showToast(`Browser launch failed: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    renderCategoryFilters() {
        return this.categories.map(category => {
            const profileCount = this.profiles.filter(p => p.category === category.id).length;
            const isDefault = ['general', 'gaming', 'business', 'personal', 'testing'].includes(category.id);
            return `
                <div class="category-filter-container">
                    <button class="category-filter ${this.selectedCategory === category.id ? 'active' : ''}"
                            onclick="app.filterByCategory('${category.id}')"
                            style="border-color: ${category.color}">
                        <i class="fas fa-tag mr-1" style="color: ${category.color}"></i>
                        ${category.name} (${profileCount})
                    </button>
                    ${!isDefault ? `
                        <button class="category-delete-btn" onclick="app.deleteCategory('${category.id}')"
                                title="Delete category">
                            <i class="fas fa-times"></i>
                        </button>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    filterByCategory(categoryId) {
        this.selectedCategory = categoryId;
        this.loadProfiles(); // Reload to apply filter
    }

    showCreateCategoryModal() {
        const modal = `
            <div class="modal-overlay" onclick="app.closeModal()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>Create New Category</h3>
                        <button onclick="app.closeModal()" class="modal-close">&times;</button>
                    </div>
                    <form onsubmit="app.createCategory(event)">
                        <div class="modal-body space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Category Name</label>
                                <input type="text" name="name" required class="w-full p-2 border rounded-lg"
                                       placeholder="e.g., Gaming, Business, Personal">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Description</label>
                                <textarea name="description" class="w-full p-2 border rounded-lg" rows="3"
                                          placeholder="Optional description for this category"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Color</label>
                                <div class="flex space-x-2">
                                    <input type="color" name="color" value="#007bff" class="w-12 h-10 border rounded">
                                    <input type="text" name="colorText" value="#007bff" class="flex-1 p-2 border rounded-lg">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" onclick="app.closeModal()" class="btn-secondary">Cancel</button>
                            <button type="submit" class="btn-primary">Create Category</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modal);
    }

    async createCategory(event) {
        event.preventDefault();
        const formData = new FormData(event.target);

        try {
            const categoryData = {
                id: await window.electronAPI.utils.generateId(),
                name: formData.get('name'),
                description: formData.get('description'),
                color: formData.get('color'),
                createdAt: Date.now(),
                updatedAt: Date.now()
            };

            await window.electronAPI.categories.create(categoryData);
            this.categories = await window.electronAPI.categories.getAll();

            this.closeModal();
            this.showToast('Category created successfully', 'success');

            // Reload profiles to show new category filter
            if (this.currentPage === 'profiles') {
                this.loadProfiles();
            }
        } catch (error) {
            console.error('Failed to create category:', error);
            this.showToast('Failed to create category', 'error');
        }
    }

    async deleteCategory(categoryId) {
        // Check if category has profiles
        const profilesInCategory = this.profiles.filter(p => p.category === categoryId);

        if (profilesInCategory.length > 0) {
            const confirmMessage = `This category contains ${profilesInCategory.length} profile(s). Deleting it will move these profiles to "General" category. Continue?`;
            if (!confirm(confirmMessage)) {
                return;
            }
        } else {
            if (!confirm('Are you sure you want to delete this category?')) {
                return;
            }
        }

        try {
            // Move profiles to general category
            if (profilesInCategory.length > 0) {
                for (const profile of profilesInCategory) {
                    await window.electronAPI.profiles.update(profile.id, { category: 'general' });
                }
            }

            // Delete category
            await window.electronAPI.categories.delete(categoryId);

            // Reload data
            this.categories = await window.electronAPI.categories.getAll();
            this.profiles = await window.electronAPI.profiles.getAll();

            // Reset filter if deleted category was selected
            if (this.selectedCategory === categoryId) {
                this.selectedCategory = null;
            }

            this.showToast('Category deleted successfully', 'success');

            // Reload profiles page if currently viewing
            if (this.currentPage === 'profiles') {
                this.loadProfiles();
            }
        } catch (error) {
            console.error('Failed to delete category:', error);
            this.showToast('Failed to delete category', 'error');
        }
    }

    closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }

        // Also close the new modal system
        const newModal = document.getElementById('modal');
        if (newModal) {
            newModal.classList.add('hidden');
        }

        // Reset editing state
        this.editingProfile = null;
    }

    showModal(content, options = {}) {
        const modal = document.getElementById('modal');
        const modalContent = document.getElementById('modal-content');
        modalContent.innerHTML = content;
        modal.classList.remove('hidden');

        // Set modal options
        modal.dataset.preventClose = options.preventClose || 'false';

        // Add click outside handler only if preventClose is false
        modal.onclick = (e) => {
            if (e.target === modal && modal.dataset.preventClose !== 'true') {
                this.closeModal();
            }
        };

        // Prevent modal content clicks from closing modal
        modalContent.onclick = (e) => {
            e.stopPropagation();
        };

        // Focus first input if available with longer delay and multiple attempts
        setTimeout(() => {
            this.focusFirstInput(modalContent);
            this.ensureInputsClickable();
        }, 100);

        // Backup focus attempt
        setTimeout(() => {
            this.focusFirstInput(modalContent);
            this.ensureInputsClickable();
        }, 500);
    }

    focusFirstInput(container) {
        const firstInput = container.querySelector('input[type="text"], input[type="email"], input[type="password"], textarea, select');
        if (firstInput && firstInput.offsetParent !== null) { // Check if element is visible
            try {
                // Remove any existing readonly attribute
                firstInput.removeAttribute('readonly');

                // Force focus with multiple methods
                firstInput.focus();
                firstInput.click();

                // Force cursor to end of text if there's existing content
                if (firstInput.type === 'text' || firstInput.tagName === 'TEXTAREA') {
                    const len = firstInput.value.length;
                    firstInput.setSelectionRange(len, len);
                }

                // Add event listeners to ensure input works
                firstInput.addEventListener('click', function(e) {
                    e.stopPropagation();
                    this.focus();
                }, { once: true });

                console.log('Successfully focused input:', firstInput);
            } catch (error) {
                console.warn('Failed to focus input:', error);
            }
        } else {
            console.warn('No focusable input found or input not visible');
        }
    }

    showNotification(message, type = 'info') {
        this.showToast(message, type);
    }

    toggleImportExportMenu() {
        const menu = document.getElementById('import-export-menu');
        if (menu) {
            menu.classList.toggle('hidden');
        }
    }

    async exportProfilesToRAR() {
        try {
            // Hide menu
            document.getElementById('import-export-menu').classList.add('hidden');

            // Get selected profiles or all profiles
            const profileIds = this.getSelectedProfileIds() || this.profiles.map(p => p.id);

            if (profileIds.length === 0) {
                this.showToast('No profiles to export', 'warning');
                return;
            }

            // Select export location
            const exportPath = await window.electronAPI.file.selectSaveFile({
                title: 'Export Profiles to RAR',
                defaultPath: `facebot-profiles-${new Date().toISOString().split('T')[0]}.rar`,
                filters: [
                    { name: 'RAR Archives', extensions: ['rar'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!exportPath || exportPath.canceled) {
                return;
            }

            this.showLoading(`Exporting ${profileIds.length} profiles to RAR...`);

            const result = await window.electronAPI.importExport.exportToRAR(profileIds, exportPath.filePath);

            if (result.success) {
                this.showToast(`Successfully exported ${result.profileCount} profiles`, 'success');
                if (result.errors.length > 0) {
                    console.warn('Export warnings:', result.errors);
                }
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('Export failed:', error);
            this.showToast(`Export failed: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async importProfilesFromRAR() {
        try {
            // Hide menu
            document.getElementById('import-export-menu').classList.add('hidden');

            // Select RAR file
            const rarFile = await window.electronAPI.file.selectFile({
                title: 'Import Profiles from RAR',
                filters: [
                    { name: 'RAR Archives', extensions: ['rar'] },
                    { name: '7-Zip Archives', extensions: ['7z'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!rarFile || rarFile.canceled) {
                return;
            }

            // Show import options modal
            this.showImportOptionsModal(rarFile.filePaths[0]);

        } catch (error) {
            console.error('Import failed:', error);
            this.showToast(`Import failed: ${error.message}`, 'error');
        }
    }

    showImportOptionsModal(rarPath) {
        // Store the path in a data attribute to avoid escaping issues
        const escapedPath = rarPath.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
        const modal = `
            <div class="modal-overlay" onclick="app.closeModal()">
                <div class="modal-content" onclick="event.stopPropagation()" data-rar-path="${escapedPath}">
                    <div class="modal-header">
                        <h3>Import Options</h3>
                        <button onclick="app.closeModal()" class="modal-close">&times;</button>
                    </div>
                    <form onsubmit="app.executeImportFromRAR(event)">
                        <div class="modal-body space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Import File</label>
                                <input type="text" value="${rarPath}" readonly class="w-full p-2 border rounded-lg bg-gray-100">
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="overwrite" id="overwrite" class="mr-2">
                                <label for="overwrite" class="text-sm">Overwrite existing profiles</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="generateNewIds" id="generateNewIds" class="mr-2" checked>
                                <label for="generateNewIds" class="text-sm">Generate new profile IDs</label>
                            </div>
                            <div>
                                <label for="importCategory" class="block text-sm font-medium mb-2">Assign to Category (Optional)</label>
                                <select name="category" id="importCategory" class="w-full p-2 border rounded-lg">
                                    <option value="">Keep original categories</option>
                                    ${this.categories.map(cat => `
                                        <option value="${cat.id}">${cat.name}</option>
                                    `).join('')}
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" onclick="app.closeModal()" class="btn-secondary">Cancel</button>
                            <button type="submit" class="btn-primary">Import Profiles</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modal);
    }

    async executeImportFromRAR(event) {
        event.preventDefault();
        const formData = new FormData(event.target);

        // Get the RAR path from the modal's data attribute
        const modalContent = event.target.closest('.modal-content');
        const rarPath = modalContent.getAttribute('data-rar-path').replace(/\\\\/g, '\\').replace(/\\'/g, "'");

        try {
            const options = {
                overwrite: formData.get('overwrite') === 'on',
                generateNewIds: formData.get('generateNewIds') === 'on',
                assignCategory: formData.get('category') || null
            };

            this.closeModal();
            this.showLoading('Importing profiles from RAR...');

            const result = await window.electronAPI.importExport.importFromRAR(rarPath, options);

            if (result.success) {
                this.showToast(`Successfully imported ${result.importedCount} profiles`, 'success');

                if (result.skippedCount > 0) {
                    console.log('Skipped profiles:', result.skipped);
                }
                if (result.errorCount > 0) {
                    console.warn('Import errors:', result.errors);
                }

                // Reload data
                await this.loadInitialData();
                if (this.currentPage === 'profiles') {
                    await this.loadProfiles();
                }
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('Import failed:', error);
            this.showToast(`Import failed: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    getSelectedProfileIds() {
        // For now, return null to export all profiles
        // In future, implement profile selection UI
        return null;
    }

    // Automation page methods
    async loadAutomationData() {
        try {
            // Load active automations
            this.activeAutomations = await window.electronAPI.automation.getActiveAutomations();

            // Load scheduled automations
            this.scheduledAutomations = await window.electronAPI.schedules.getAll();

            // Load smart selector stats
            this.selectorStats = await window.electronAPI.automation.getSelectorStats();

        } catch (error) {
            console.error('Failed to load automation data:', error);
        }
    }

    renderActiveAutomations() {
        if (!this.activeAutomations || this.activeAutomations.length === 0) {
            return `
                <div class="col-span-full text-center py-8">
                    <i class="fas fa-robot text-3xl text-gray-300 mb-2"></i>
                    <p class="text-gray-500">No active automations</p>
                </div>
            `;
        }

        return this.activeAutomations.map(automation => `
            <div class="card p-4">
                <div class="flex justify-between items-start mb-2">
                    <h5 class="font-semibold">${automation.type}</h5>
                    <span class="status-indicator ${automation.status === 'running' ? 'status-active' : 'status-inactive'}"></span>
                </div>
                <div class="text-sm text-gray-600 space-y-1">
                    <div><strong>Profile:</strong> ${automation.profileId}</div>
                    <div><strong>Progress:</strong> ${automation.completedGroups || 0}/${automation.totalGroups || 0}</div>
                    <div><strong>Started:</strong> ${new Date(automation.startTime).toLocaleTimeString()}</div>
                </div>
                <div class="mt-3 flex space-x-2">
                    <button class="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded text-sm" onclick="app.stopAutomation('${automation.id}')">
                        <i class="fas fa-stop mr-1"></i>Stop
                    </button>
                    <button class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm" onclick="app.viewAutomationDetails('${automation.id}')">
                        <i class="fas fa-eye mr-1"></i>Details
                    </button>
                </div>
            </div>
        `).join('');
    }

    renderScheduledAutomations() {
        if (!this.scheduledAutomations || this.scheduledAutomations.length === 0) {
            return `
                <div class="text-center py-8">
                    <i class="fas fa-calendar-alt text-3xl text-gray-300 mb-2"></i>
                    <p class="text-gray-500">No scheduled automations</p>
                </div>
            `;
        }

        return this.scheduledAutomations.map(schedule => `
            <div class="card p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <h5 class="font-semibold">${schedule.name}</h5>
                        <p class="text-sm text-gray-600">${schedule.type}</p>
                        <p class="text-xs text-gray-500">Cron: ${schedule.cronExpression}</p>
                        <p class="text-xs text-gray-500">Next run: ${schedule.nextRun ? new Date(schedule.nextRun).toLocaleString() : 'Not scheduled'}</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="status-indicator ${schedule.isActive ? 'status-active' : 'status-inactive'}"></span>
                        <div class="flex space-x-1">
                            <button class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded text-xs" onclick="app.editSchedule('${schedule.id}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-xs" onclick="app.deleteSchedule('${schedule.id}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">
                    Runs: ${schedule.runCount} | Success: ${schedule.successCount} | Failed: ${schedule.failureCount}
                </div>
            </div>
        `).join('');
    }

    renderSelectorStats() {
        if (!this.selectorStats) {
            return '<p class="text-gray-500">Loading selector statistics...</p>';
        }

        return `
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">${this.selectorStats.totalSelectors || 0}</div>
                    <div class="text-sm text-gray-600">Total Selectors</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">${this.selectorStats.successRate || 0}%</div>
                    <div class="text-sm text-gray-600">Success Rate</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">${this.selectorStats.learnedSelectors || 0}</div>
                    <div class="text-sm text-gray-600">Learned Selectors</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">${this.selectorStats.lastUpdate || 'Never'}</div>
                    <div class="text-sm text-gray-600">Last Update</div>
                </div>
            </div>
        `;
    }

    showGroupSharingModal() {
        const modal = `
            <div class="modal-overlay" onclick="app.closeModal()">
                <div class="modal-content max-w-3xl max-h-[90vh] flex flex-col" onclick="event.stopPropagation()">
                    <div class="modal-header flex-shrink-0">
                        <h3>Start Group Sharing</h3>
                        <button onclick="app.closeModal()" class="modal-close">&times;</button>
                    </div>
                    <form onsubmit="app.startGroupSharing(event)" class="flex flex-col h-full">
                        <div class="modal-body space-y-4 overflow-y-auto flex-1 px-6 py-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Select Profile</label>
                                <select name="profileId" required class="w-full p-2 border rounded-lg">
                                    <option value="">Choose a profile...</option>
                                    ${this.profiles.map(profile => `
                                        <option value="${profile.id}">${profile.name} (${profile.type})</option>
                                    `).join('')}
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Content to Share</label>
                                <textarea name="content" required class="w-full p-2 border rounded-lg" rows="4"
                                          placeholder="Enter the content you want to share to groups..."></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Facebook Groups (one URL per line)</label>
                                <textarea name="groups" required class="w-full p-2 border rounded-lg" rows="6"
                                          placeholder="https://facebook.com/groups/group1&#10;https://facebook.com/groups/group2&#10;..."></textarea>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Min Delay (seconds)</label>
                                    <input type="number" name="minDelay" value="30" min="10" class="w-full p-2 border rounded-lg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-2">Max Delay (seconds)</label>
                                    <input type="number" name="maxDelay" value="60" min="20" class="w-full p-2 border rounded-lg">
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center">
                                    <input type="checkbox" name="randomScrolling" checked class="mr-2">
                                    Random Scrolling (Anti-detection)
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="antiDetection" checked class="mr-2">
                                    Anti-detection Features
                                </label>
                            </div>
                        </div>
                        <div class="modal-footer flex-shrink-0 px-6 py-4 border-t">
                            <button type="button" onclick="app.closeModal()" class="btn-secondary mr-3">Cancel</button>
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-share-alt mr-2"></i>Start Group Sharing
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modal);
    }

    async startGroupSharing(event) {
        event.preventDefault();
        const formData = new FormData(event.target);

        try {
            const groups = formData.get('groups').split('\n')
                .map(url => url.trim())
                .filter(url => url.length > 0)
                .map(url => ({ url, name: url.split('/').pop() }));

            if (groups.length === 0) {
                throw new Error('Please enter at least one group URL');
            }

            const options = {
                profileId: formData.get('profileId'),
                content: formData.get('content'),
                groups: groups,
                sequenceDelay: {
                    min: parseInt(formData.get('minDelay')) * 1000,
                    max: parseInt(formData.get('maxDelay')) * 1000
                },
                randomScrolling: formData.get('randomScrolling') === 'on',
                antiDetection: formData.get('antiDetection') === 'on'
            };

            this.closeModal();
            this.showLoading('Starting group sharing automation...');

            const result = await window.electronAPI.automation.startGroupSharing(options);

            if (result.success) {
                this.showToast(`Group sharing started for ${groups.length} groups`, 'success');
                await this.loadAutomationData();
                if (this.currentPage === 'automation') {
                    await this.loadAutomation();
                }
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('Failed to start group sharing:', error);
            this.showToast(`Failed to start group sharing: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    showScheduleModal() {
        const modal = `
            <div class="modal-overlay" onclick="app.closeModal()">
                <div class="modal-content max-w-2xl" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>Schedule Automation</h3>
                        <button onclick="app.closeModal()" class="modal-close">&times;</button>
                    </div>
                    <form onsubmit="app.createSchedule(event)">
                        <div class="modal-body space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Schedule Name</label>
                                <input type="text" name="name" required class="w-full p-2 border rounded-lg"
                                       placeholder="e.g., Daily Group Sharing">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Automation Type</label>
                                <select name="type" required class="w-full p-2 border rounded-lg">
                                    <option value="">Choose automation type...</option>
                                    <option value="group_sharing">Group Sharing</option>
                                    <option value="campaign">Campaign</option>
                                    <option value="profile_rotation">Profile Rotation</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Schedule (Cron Expression)</label>
                                <select name="cronPreset" class="w-full p-2 border rounded-lg mb-2" onchange="app.updateCronExpression(this)">
                                    <option value="">Choose preset or enter custom...</option>
                                    <option value="0 9 * * *">Daily at 9:00 AM</option>
                                    <option value="0 9 * * 1-5">Weekdays at 9:00 AM</option>
                                    <option value="0 9,15 * * *">Daily at 9:00 AM and 3:00 PM</option>
                                    <option value="0 9 * * 1">Weekly on Monday at 9:00 AM</option>
                                    <option value="0 */2 * * *">Every 2 hours</option>
                                </select>
                                <input type="text" name="cronExpression" required class="w-full p-2 border rounded-lg"
                                       placeholder="0 9 * * * (minute hour day month weekday)">
                                <p class="text-xs text-gray-500 mt-1">Use cron format: minute hour day month weekday</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Automation Configuration (JSON)</label>
                                <textarea name="config" required class="w-full p-2 border rounded-lg" rows="6"
                                          placeholder='{"profileId": "profile-id", "content": "Your content", "groups": [...]}'></textarea>
                                <p class="text-xs text-gray-500 mt-1">Enter automation configuration in JSON format</p>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" onclick="app.closeModal()" class="btn-secondary">Cancel</button>
                            <button type="submit" class="btn-primary">Create Schedule</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modal);
    }

    updateCronExpression(select) {
        const cronInput = document.querySelector('input[name="cronExpression"]');
        if (cronInput && select.value) {
            cronInput.value = select.value;
        }
    }

    async createSchedule(event) {
        event.preventDefault();
        const formData = new FormData(event.target);

        try {
            const config = JSON.parse(formData.get('config'));

            const scheduleConfig = {
                name: formData.get('name'),
                type: formData.get('type'),
                cronExpression: formData.get('cronExpression'),
                automationConfig: config,
                isActive: true
            };

            this.closeModal();
            this.showLoading('Creating schedule...');

            const result = await window.electronAPI.schedules.create(scheduleConfig);

            if (result.success) {
                this.showToast('Schedule created successfully', 'success');
                await this.loadAutomationData();
                if (this.currentPage === 'automation') {
                    await this.loadAutomation();
                }
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('Failed to create schedule:', error);
            this.showToast(`Failed to create schedule: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async stopAutomation(automationId) {
        try {
            const result = await window.electronAPI.automation.stop(automationId);
            if (result.success) {
                this.showToast('Automation stopped', 'success');
                await this.loadAutomationData();
                if (this.currentPage === 'automation') {
                    await this.loadAutomation();
                }
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            this.showToast(`Failed to stop automation: ${error.message}`, 'error');
        }
    }

    async deleteSchedule(scheduleId) {
        if (!confirm('Are you sure you want to delete this schedule?')) {
            return;
        }

        try {
            const result = await window.electronAPI.schedules.delete(scheduleId);
            if (result.success) {
                this.showToast('Schedule deleted', 'success');
                await this.loadAutomationData();
                if (this.currentPage === 'automation') {
                    await this.loadAutomation();
                }
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            this.showToast(`Failed to delete schedule: ${error.message}`, 'error');
        }
    }

    async forceCleanupProfiles() {
        if (!confirm('This will force cleanup all stuck profiles and browser windows. Continue?')) {
            return;
        }

        try {
            this.showLoading('Force cleaning up stuck profiles...');
            const result = await window.electronAPI.browser.forceCleanup();

            if (result.success) {
                this.showToast('Force cleanup completed successfully', 'success');
                // Refresh profiles to show updated status
                await this.loadInitialData();
                this.refreshCurrentPage();
            } else {
                this.showToast(`Force cleanup failed: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error during force cleanup: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Simple Automation Methods
    async runLikeAutomation() {
        try {
            console.log('🚀 [DEBUG] Frontend: Starting Like automation...');

            // Get automation settings from UI
            const automationSettings = await this.getAutomationTargetSettings();
            console.log('🎯 [DEBUG] Frontend: Automation settings from UI:', automationSettings);

            this.showLoading('Starting Like automation...');

            // Send settings as targetSettings in options
            const options = {
                targetSettings: automationSettings
            };
            console.log('📤 [DEBUG] Frontend: Sending options to backend:', options);

            const result = await window.electronAPI.automation.runLike(options);
            console.log('📥 [DEBUG] Frontend: Result from backend:', result);

            if (result.success) {
                this.showToast('Like automation started successfully', 'success');
            } else {
                this.showToast(`Like automation failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('❌ [DEBUG] Frontend: Error starting like automation:', error);
            this.showToast(`Error starting like automation: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async runCommentAutomation() {
        try {
            console.log('🚀 [DEBUG] Frontend: Starting Comment automation...');

            // Get automation settings from UI
            const automationSettings = await this.getAutomationTargetSettings();
            console.log('🎯 [DEBUG] Frontend: Comment automation settings from UI:', JSON.stringify(automationSettings, null, 2));

            this.showLoading('Starting Comment automation...');

            // Send settings as targetSettings in options (consistent with Like automation)
            const options = {
                targetSettings: automationSettings
            };
            console.log('📤 [DEBUG] Frontend: Sending Comment options to backend:', options);

            const result = await window.electronAPI.automation.runComment(options);
            console.log('📥 [DEBUG] Frontend: Comment result from backend:', result);

            if (result.success) {
                this.showToast('Comment automation started successfully', 'success');
            } else {
                this.showToast(`Comment automation failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('❌ [DEBUG] Frontend: Error starting comment automation:', error);
            this.showToast(`Error starting comment automation: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async runShareTimelineAutomation() {
        try {
            console.log('🚀 [DEBUG] Frontend: Starting Share Timeline automation...');

            // Get automation settings from UI
            const automationSettings = await this.getAutomationTargetSettings();
            console.log('🎯 [DEBUG] Frontend: Share Timeline automation settings from UI:', automationSettings);

            this.showLoading('Starting Share Timeline automation...');

            // Send settings as targetSettings in options (consistent with Like automation)
            const options = {
                targetSettings: automationSettings
            };
            console.log('📤 [DEBUG] Frontend: Sending Share Timeline options to backend:', options);

            const result = await window.electronAPI.automation.runShareTimeline(options);
            console.log('📥 [DEBUG] Frontend: Share Timeline result from backend:', result);

            if (result.success) {
                this.showToast('Share Timeline automation started successfully', 'success');
            } else {
                this.showToast(`Share Timeline automation failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('❌ [DEBUG] Frontend: Error starting share timeline automation:', error);
            this.showToast(`Error starting share timeline automation: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async runShareGroupsAutomation() {
        try {
            console.log('🚀 [DEBUG] Frontend: Starting Share Groups automation...');

            // Get automation settings from UI
            const automationSettings = await this.getAutomationTargetSettings();
            console.log('🎯 [DEBUG] Frontend: Share Groups automation settings from UI:', automationSettings);

            this.showLoading('Starting Share Groups automation...');

            // Send settings as targetSettings in options (consistent with Like automation)
            const options = {
                targetSettings: automationSettings
            };
            console.log('📤 [DEBUG] Frontend: Sending Share Groups options to backend:', options);

            const result = await window.electronAPI.automation.runShareGroups(options);
            console.log('📥 [DEBUG] Frontend: Share Groups result from backend:', result);

            if (result.success) {
                this.showToast('Share Groups automation started successfully', 'success');
            } else {
                this.showToast(`Share Groups automation failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('❌ [DEBUG] Frontend: Error starting share groups automation:', error);
            this.showToast(`Error starting share groups automation: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Scheduling Methods
    async loadScheduleData() {
        try {
            this.activeSchedules = await window.electronAPI.schedules.getAll();
            this.scheduleSettings = await window.electronAPI.settings.getScheduleSettings();
        } catch (error) {
            console.error('Failed to load schedule data:', error);
            this.activeSchedules = [];
            this.scheduleSettings = {};
        }
    }

    renderActiveSchedules() {
        if (!this.activeSchedules || this.activeSchedules.length === 0) {
            return '<p class="text-gray-500 text-center py-4">No active schedules</p>';
        }

        return this.activeSchedules.map(schedule => `
            <div class="card p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-2 rounded-full ${this.getScheduleIconColor(schedule.type)} mr-3">
                            <i class="${this.getScheduleIcon(schedule.type)}"></i>
                        </div>
                        <div>
                            <h5 class="font-medium">${schedule.name}</h5>
                            <p class="text-sm text-gray-600">${schedule.description}</p>
                            <p class="text-xs text-gray-500">Next run: ${new Date(schedule.nextRun).toLocaleString()}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="status-indicator ${schedule.enabled ? 'status-active' : 'status-inactive'}"></span>
                        <button class="text-blue-500 hover:text-blue-700" onclick="app.editSchedule('${schedule.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="text-red-500 hover:text-red-700" onclick="app.deleteSchedule('${schedule.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getScheduleIcon(type) {
        const icons = {
            like: 'fas fa-heart',
            comment: 'fas fa-comment',
            share_timeline: 'fas fa-share',
            share_groups: 'fas fa-share-alt'
        };
        return icons[type] || 'fas fa-clock';
    }

    getScheduleIconColor(type) {
        const colors = {
            like: 'bg-red-100 text-red-600',
            comment: 'bg-blue-100 text-blue-600',
            share_timeline: 'bg-green-100 text-green-600',
            share_groups: 'bg-purple-100 text-purple-600'
        };
        return colors[type] || 'bg-gray-100 text-gray-600';
    }

    async createQuickSchedule(template) {
        const templates = {
            like_hourly: {
                name: 'Like Every Hour',
                type: 'like',
                description: 'Auto-like posts every hour',
                cronExpression: '0 * * * *', // Every hour
                automationConfig: { type: 'like' },
                isActive: true
            },
            comment_daily: {
                name: 'Comment Daily',
                type: 'comment',
                description: 'Auto-comment once per day',
                cronExpression: '0 9 * * *', // 9 AM daily
                automationConfig: { type: 'comment' },
                isActive: true
            },
            share_timeline_weekly: {
                name: 'Share Weekly',
                type: 'share_timeline',
                description: 'Share to timeline weekly',
                cronExpression: '0 10 * * 1', // 10 AM every Monday
                automationConfig: { type: 'share_timeline' },
                isActive: true
            },
            share_groups_custom: {
                name: 'Custom Groups',
                type: 'share_groups',
                description: 'Custom group sharing schedule',
                cronExpression: '0 14 * * 1,3,5', // 2 PM on Mon, Wed, Fri
                automationConfig: { type: 'share_groups' },
                isActive: true
            }
        };

        const scheduleData = templates[template];
        if (!scheduleData) {
            this.showToast('Invalid template selected', 'error');
            return;
        }

        try {
            this.showLoading('Creating schedule...');
            const result = await window.electronAPI.schedules.create(scheduleData);

            if (result.success) {
                this.showToast('Schedule created successfully', 'success');
                await this.loadScheduleData();
                this.refreshCurrentPage();
            } else {
                this.showToast(`Failed to create schedule: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error creating schedule: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async saveScheduleSettings() {
        try {
            const settings = {
                defaultDelay: parseInt(document.getElementById('default-delay').value),
                maxConcurrent: parseInt(document.getElementById('max-concurrent').value),
                workStart: document.getElementById('work-start').value,
                workEnd: document.getElementById('work-end').value,
                weekendMode: document.getElementById('weekend-mode').value
            };

            this.showLoading('Saving schedule settings...');
            const result = await window.electronAPI.settings.saveScheduleSettings(settings);

            if (result.success) {
                this.showToast('Schedule settings saved successfully', 'success');
            } else {
                this.showToast(`Failed to save settings: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error saving settings: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    showCreateScheduleModal() {
        // Implementation for creating custom schedules
        this.showToast('Custom schedule creation coming soon!', 'info');
    }

    async editSchedule(scheduleId) {
        // Implementation for editing schedules
        this.showToast('Schedule editing coming soon!', 'info');
    }

    async deleteSchedule(scheduleId) {
        if (confirm('Are you sure you want to delete this schedule?')) {
            try {
                const result = await window.electronAPI.schedules.delete(scheduleId);
                if (result.success) {
                    this.showToast('Schedule deleted successfully', 'success');
                    await this.loadScheduleData();
                    this.refreshCurrentPage();
                } else {
                    this.showToast(`Failed to delete schedule: ${result.error}`, 'error');
                }
            } catch (error) {
                this.showToast(`Error deleting schedule: ${error.message}`, 'error');
            }
        }
    }

    async loadSettings() {
        // Always reload settings from backend to ensure fresh data
        await this.loadCurrentSettings();

        const content = `
            <div class="space-y-6">
                <!-- Export/Import Settings -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Backup & Restore</h3>
                    <p class="text-gray-600 mb-4">Export your settings, profiles, and content to backup file or import from existing backup.</p>
                    <div class="flex flex-wrap gap-3">
                        <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg" onclick="app.exportSettings()">
                            <i class="fas fa-download mr-2"></i>Export Settings
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg" onclick="app.importSettings()">
                            <i class="fas fa-upload mr-2"></i>Import Settings
                        </button>
                    </div>
                </div>

                <!-- Target Selection Settings -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Automation Target Settings</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-3">Run Automation For</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="automation-target" value="all" class="mr-2" checked onchange="app.updateTargetSelection()">
                                    <span class="text-sm">All Profiles</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="automation-target" value="category" class="mr-2" onchange="app.updateTargetSelection()">
                                    <span class="text-sm">Specific Category</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="automation-target" value="profile" class="mr-2" onchange="app.updateTargetSelection()">
                                    <span class="text-sm">Specific Profile</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="automation-target" value="redlist" class="mr-2" onchange="app.updateTargetSelection()">
                                    <span class="text-sm">Red List Only</span>
                                </label>
                            </div>
                        </div>
                        <div id="target-selection-container" class="hidden">
                            <label class="block text-sm font-medium mb-2" id="target-selection-label">Select Target</label>
                            <select id="target-selection" class="w-full p-2 border rounded-lg">
                                <!-- Options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Decoy Links Settings -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Anti-Detection Settings</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium mb-2">Decoy Links Before Action</label>
                            <input type="number" id="decoy-count" value="2" class="w-full p-2 border rounded-lg" min="0" max="10">
                            <p class="text-xs text-gray-500 mt-1">Number of random decoy links to visit before each action</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Decoy Link Delay (seconds)</label>
                            <input type="number" id="decoy-delay" value="5" class="w-full p-2 border rounded-lg" min="1" max="30">
                            <p class="text-xs text-gray-500 mt-1">Time to spend on each decoy link</p>
                        </div>
                        <div class="col-span-2">
                            <div class="flex items-center justify-between mb-2">
                                <label class="block text-sm font-medium">Decoy Links</label>
                                <button class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-3 rounded text-sm" onclick="app.showDecoyLinksModal()">
                                    <i class="fas fa-list mr-1"></i>Manage Decoy Links
                                </button>
                            </div>
                            <p class="text-xs text-gray-500">Click "Manage Decoy Links" to add/edit decoy URLs</p>
                        </div>
                    </div>
                </div>

                <!-- Content Management -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Content Management</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <button class="bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg w-full" onclick="app.showCommentsModal()">
                                <i class="fas fa-comment mb-2"></i>
                                <div class="text-sm font-medium">Manage Comments</div>
                                <div class="text-xs opacity-75" id="comments-count">0 comments</div>
                            </button>
                        </div>
                        <div class="text-center">
                            <button class="bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg w-full" onclick="app.showLinksModal()">
                                <i class="fas fa-link mb-2"></i>
                                <div class="text-sm font-medium">Manage Links</div>
                                <div class="text-xs opacity-75" id="links-count">0 links</div>
                            </button>
                        </div>
                        <div class="text-center">
                            <button class="bg-purple-500 hover:bg-purple-600 text-white py-3 px-4 rounded-lg w-full" onclick="app.showGroupLinksModal()">
                                <i class="fas fa-users mb-2"></i>
                                <div class="text-sm font-medium">Manage Group Links</div>
                                <div class="text-xs opacity-75" id="group-links-count">0 groups</div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Automation Settings -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Automation Settings</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium mb-2">Like Settings</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="checkbox" id="enable-likes" class="mr-2" checked>
                                    <label for="enable-likes" class="text-sm">Enable Like Automation</label>
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Delay between likes (seconds)</label>
                                    <input type="number" id="like-delay" value="3" class="w-full p-2 border rounded text-sm" min="1" max="60">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Max likes per session (0 = unlimited)</label>
                                    <input type="number" id="max-likes" value="50" class="w-full p-2 border rounded text-sm" min="0" max="500">
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Comment Settings</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="checkbox" id="enable-comments" class="mr-2" checked>
                                    <label for="enable-comments" class="text-sm">Enable Comment Automation</label>
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Delay between comments (seconds)</label>
                                    <input type="number" id="comment-delay" value="5" class="w-full p-2 border rounded text-sm" min="1" max="120">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Max comments per session (0 = unlimited)</label>
                                    <input type="number" id="max-comments" value="20" class="w-full p-2 border rounded text-sm" min="0" max="100">
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Share Timeline Settings</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="checkbox" id="enable-share-timeline" class="mr-2" checked>
                                    <label for="enable-share-timeline" class="text-sm">Enable Timeline Sharing</label>
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Delay between shares (seconds)</label>
                                    <input type="number" id="share-timeline-delay" value="10" class="w-full p-2 border rounded text-sm" min="1" max="300">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Max shares per session (0 = unlimited)</label>
                                    <input type="number" id="max-timeline-shares" value="10" class="w-full p-2 border rounded text-sm" min="0" max="50">
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">Share Groups Settings</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="checkbox" id="enable-share-groups" class="mr-2" checked>
                                    <label for="enable-share-groups" class="text-sm">Enable Group Sharing</label>
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Delay between group shares (seconds)</label>
                                    <input type="number" id="share-groups-delay" value="15" class="w-full p-2 border rounded text-sm" min="1" max="600">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">Max groups per session (0 = unlimited)</label>
                                    <input type="number" id="max-group-shares" value="5" class="w-full p-2 border rounded text-sm" min="0" max="20">
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Scheduling Settings -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Scheduling Settings</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium mb-2">Default Delay (seconds)</label>
                            <input type="number" id="default-delay" value="${this.scheduleSettings?.defaultDelay || 30}"
                                   class="w-full p-2 border rounded-lg" min="1" max="300">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Max Concurrent</label>
                            <input type="number" id="max-concurrent" value="${this.scheduleSettings?.maxConcurrent || 3}"
                                   class="w-full p-2 border rounded-lg" min="1" max="10">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Work Start Time</label>
                            <input type="time" id="work-start" value="${this.scheduleSettings?.workStart || '09:00'}"
                                   class="w-full p-2 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Work End Time</label>
                            <input type="time" id="work-end" value="${this.scheduleSettings?.workEnd || '17:00'}"
                                   class="w-full p-2 border rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Weekend Mode</label>
                            <select id="weekend-mode" class="w-full p-2 border rounded-lg">
                                <option value="disabled" ${this.scheduleSettings?.weekendMode === 'disabled' ? 'selected' : ''}>Disabled</option>
                                <option value="reduced" ${this.scheduleSettings?.weekendMode === 'reduced' ? 'selected' : ''}>Reduced</option>
                                <option value="normal" ${this.scheduleSettings?.weekendMode === 'normal' ? 'selected' : ''}>Normal</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg" onclick="app.showCreateScheduleModal()">
                            <i class="fas fa-plus mr-2"></i>Create Custom Schedule
                        </button>
                    </div>
                </div>

                <!-- Red List Management -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Red List Management</h3>
                    <p class="text-sm text-gray-600 mb-4">Manage profiles that have failed automation or need to be excluded.</p>

                    <div class="flex justify-between items-center mb-4">
                        <div class="space-x-2">
                            <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg" onclick="app.showRedListModal()">
                                <i class="fas fa-list mr-2"></i>View Red List
                            </button>
                            <button class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg" onclick="app.clearRedList()">
                                <i class="fas fa-trash mr-2"></i>Clear All
                            </button>
                        </div>
                        <div class="text-sm text-gray-500">
                            <span id="red-list-count">0</span> profiles in red list
                        </div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-yellow-600 mt-1 mr-2"></i>
                            <div class="text-sm text-yellow-800">
                                <p class="font-medium">About Red List:</p>
                                <ul class="mt-1 list-disc list-inside space-y-1">
                                    <li>Profiles are automatically added when automation fails</li>
                                    <li>You can manually add/remove profiles from the red list</li>
                                    <li>Red list profiles can be targeted specifically for retry automation</li>
                                    <li>Use "Red List Only" in automation target settings to focus on failed profiles</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Browser Settings -->
                <div class="card p-6">
                    <h3 class="text-lg font-semibold mb-4">Browser Settings</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium mb-2">Browser Engine</label>
                            <select id="browser-engine" class="w-full p-2 border rounded-lg" disabled>
                                <option value="chromium" selected>Playwright Chromium (Recommended)</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">Using Playwright's Chromium for optimal automation</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Headless Mode</label>
                            <select id="headless-mode" class="w-full p-2 border rounded-lg">
                                <option value="false">Visible Browser (Recommended)</option>
                                <option value="true">Headless Mode</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">User Agent</label>
                            <select id="user-agent" class="w-full p-2 border rounded-lg">
                                <option value="default">Default Chrome User Agent</option>
                                <option value="random">Random User Agent</option>
                                <option value="custom">Custom User Agent</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Page Load Timeout (seconds)</label>
                            <input type="number" id="page-timeout" value="30" class="w-full p-2 border rounded-lg" min="5" max="120">
                        </div>
                    </div>

                </div>

                <!-- Save All Settings Button -->
                <div class="card p-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                    <div class="text-center">
                        <h3 class="text-xl font-bold mb-2">Save All Settings</h3>
                        <p class="mb-4 opacity-90">Save all automation, browser, schedule, and target settings at once</p>
                        <button class="bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-colors" onclick="app.saveAllSettings()">
                            <i class="fas fa-save mr-2"></i>Save All Settings
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('page-content').innerHTML = content;

        // Wait for DOM to be ready
        await new Promise(resolve => setTimeout(resolve, 100));

        // Load current settings from backend first
        await this.loadCurrentSettings();

        // Load content counts
        await this.updateContentCounts();

        // Populate forms with current settings
        await this.populateSettingsForms();

        // Add event listeners for target automation radio buttons
        document.querySelectorAll('input[name="automation-target"]').forEach(radio => {
            radio.addEventListener('change', () => {
                console.log('Radio button changed:', radio.value);
                this.updateTargetSelection();
            });
        });

        // Wait a bit more for DOM to be fully ready, then update target selection
        await new Promise(resolve => setTimeout(resolve, 200));
        await this.updateTargetSelection();

        // Update Quick Actions buttons based on automation settings
        this.updateQuickActionsButtons();
    }

    // Load current settings from backend
    async loadCurrentSettings() {
        try {
            console.log('🔄 Loading current settings...');
            this.currentSettings = {
                automation: await window.electronAPI.settings.getAutomationSettings(),
                browser: await window.electronAPI.settings.getBrowserSettings(),
                schedule: await window.electronAPI.settings.getScheduleSettings(),
                targetAutomation: await window.electronAPI.settings.getTargetAutomationSettings()
            };
            console.log('✅ Settings loaded:', this.currentSettings);
            console.log('🎯 Target automation settings specifically:', this.currentSettings.targetAutomation);
        } catch (error) {
            console.error('Failed to load current settings:', error);
            // Set defaults if loading fails
            this.currentSettings = {
                automation: {
                    like: { enabled: true, delay: 2, maxPerSession: 50 },
                    comment: { enabled: true, delay: 3, maxPerSession: 20 },
                    shareTimeline: { enabled: true, delay: 5, maxPerSession: 10 },
                    shareGroups: { enabled: true, delay: 5, maxPerSession: 10 }
                },
                browser: {
                    engine: 'chromium',
                    headless: false,
                    userAgent: 'default',
                    pageTimeout: 30000
                },
                schedule: {
                    defaultDelay: 30,
                    maxConcurrent: 3,
                    workStart: '09:00',
                    workEnd: '17:00',
                    weekendMode: 'normal'
                },
                targetAutomation: {
                    targetType: 'all',
                    targetId: null,
                    decoySettings: { count: 2, delay: 5000 }
                }
            };
        }
    }

    // Populate all settings forms with current values
    async populateSettingsForms() {
        try {
            // Populate automation settings
            if (this.currentSettings.automation) {
                const auto = this.currentSettings.automation;

                // Like settings
                if (auto.like) {
                    this.setElementValue('enable-likes', auto.like.enabled);
                    this.setElementValue('like-delay', auto.like.delay);
                    this.setElementValue('max-likes', auto.like.maxPerSession);
                }

                // Comment settings
                if (auto.comment) {
                    this.setElementValue('enable-comments', auto.comment.enabled);
                    this.setElementValue('comment-delay', auto.comment.delay);
                    this.setElementValue('max-comments', auto.comment.maxPerSession);
                }

                // Share Timeline settings
                if (auto.shareTimeline) {
                    this.setElementValue('enable-share-timeline', auto.shareTimeline.enabled);
                    this.setElementValue('share-timeline-delay', auto.shareTimeline.delay);
                    this.setElementValue('max-timeline-shares', auto.shareTimeline.maxPerSession);
                }

                // Share Groups settings
                if (auto.shareGroups) {
                    this.setElementValue('enable-share-groups', auto.shareGroups.enabled);
                    this.setElementValue('share-groups-delay', auto.shareGroups.delay);
                    this.setElementValue('max-group-shares', auto.shareGroups.maxPerSession);
                }
            }

            // Populate browser settings
            if (this.currentSettings.browser) {
                const browser = this.currentSettings.browser;
                this.setElementValue('browser-engine', browser.engine);
                this.setElementValue('headless-mode', browser.headless ? 'true' : 'false');
                this.setElementValue('user-agent', browser.userAgent);
                this.setElementValue('page-timeout', Math.floor(browser.pageTimeout / 1000));
            }

            // Populate schedule settings
            if (this.currentSettings.schedule) {
                const schedule = this.currentSettings.schedule;
                this.setElementValue('default-delay', schedule.defaultDelay);
                this.setElementValue('max-concurrent', schedule.maxConcurrent);
                this.setElementValue('work-start', schedule.workStart);
                this.setElementValue('work-end', schedule.workEnd);
                this.setElementValue('weekend-mode', schedule.weekendMode);
            }

            // Populate target automation settings
            console.log('🔍 Checking target automation settings...');
            console.log('🔍 this.currentSettings:', this.currentSettings);
            console.log('🔍 this.currentSettings.targetAutomation:', this.currentSettings.targetAutomation);

            if (this.currentSettings.targetAutomation) {
                const target = this.currentSettings.targetAutomation;
                console.log('🎯 Loading target automation settings:', target);

                // Set radio button for target type
                const targetRadios = document.querySelectorAll('input[name="automation-target"]');
                console.log('📻 Found radio buttons:', targetRadios.length);
                console.log('📻 Radio buttons:', Array.from(targetRadios).map(r => ({ value: r.value, checked: r.checked })));

                if (targetRadios.length === 0) {
                    console.error('❌ No radio buttons found! DOM might not be ready yet.');
                    // Try again after a longer delay
                    setTimeout(() => {
                        this.populateTargetAutomationSettings(target);
                    }, 500);
                    return;
                }

                targetRadios.forEach(radio => {
                    const shouldCheck = radio.value === target.targetType;
                    radio.checked = shouldCheck;
                    console.log(`📻 Setting radio ${radio.value} to ${shouldCheck} (target: ${target.targetType})`);
                });

                console.log('📻 After setting radios:', Array.from(targetRadios).map(r => ({ value: r.value, checked: r.checked })));

                this.setElementValue('target-selection', target.targetId);
                if (target.decoySettings) {
                    this.setElementValue('decoy-count', target.decoySettings.count);
                    this.setElementValue('decoy-delay', Math.floor(target.decoySettings.delay / 1000));
                }

                // Force update target selection after setting radio buttons
                setTimeout(() => {
                    console.log('🔄 Updating target selection...');
                    this.updateTargetSelection();
                }, 200);
            } else {
                console.warn('⚠️ No target automation settings found in currentSettings');
                console.warn('⚠️ Available settings keys:', Object.keys(this.currentSettings || {}));
            }

        } catch (error) {
            console.error('Failed to populate settings forms:', error);
        }
    }

    // Separate method for populating target automation settings
    populateTargetAutomationSettings(target) {
        try {
            console.log('🎯 Populating target automation settings (retry):', target);

            // Set radio button for target type
            const targetRadios = document.querySelectorAll('input[name="automation-target"]');
            console.log('📻 Found radio buttons (retry):', targetRadios.length);

            if (targetRadios.length === 0) {
                console.error('❌ Still no radio buttons found after retry!');
                return;
            }

            targetRadios.forEach(radio => {
                const shouldCheck = radio.value === target.targetType;
                radio.checked = shouldCheck;
                console.log(`📻 Setting radio ${radio.value} to ${shouldCheck} (target: ${target.targetType})`);
            });

            this.setElementValue('target-selection', target.targetId);
            if (target.decoySettings) {
                this.setElementValue('decoy-count', target.decoySettings.count);
                this.setElementValue('decoy-delay', Math.floor(target.decoySettings.delay / 1000));
            }

            // Force update target selection after setting radio buttons
            setTimeout(() => {
                console.log('🔄 Updating target selection (retry)...');
                this.updateTargetSelection();
            }, 100);

        } catch (error) {
            console.error('❌ Failed to populate target automation settings (retry):', error);
        }
    }

    // Helper function to set element value safely
    setElementValue(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = value;
            } else {
                element.value = value;
            }
        }
    }

    async updateTargetSelection() {
        console.log('🔄 updateTargetSelection() called');
        const container = document.getElementById('target-selection-container');
        const label = document.getElementById('target-selection-label');
        const select = document.getElementById('target-selection');

        console.log('🔍 Target selection elements:', {
            container: !!container,
            label: !!label,
            select: !!select
        });

        if (!container || !label || !select) {
            console.log('❌ Target selection elements not found, skipping update');
            return;
        }

        // Get selected radio button value
        const selectedRadio = document.querySelector('input[name="automation-target"]:checked');
        console.log('📻 Selected radio button:', selectedRadio?.value);

        if (!selectedRadio) {
            console.log('❌ No target radio button selected');
            return;
        }

        const targetType = selectedRadio.value;
        const currentTargetId = this.currentSettings?.targetAutomation?.targetId;

        console.log('🎯 Target type:', targetType);
        console.log('🎯 Current target ID:', currentTargetId);

        if (targetType === 'all' || targetType === 'redlist') {
            console.log('🔒 Hiding target selection container for type:', targetType);
            container.classList.add('hidden');
            return;
        }

        container.classList.remove('hidden');
        select.innerHTML = '';

        if (targetType === 'category') {
            label.textContent = 'Select Category';
            this.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                if (currentTargetId && category.id === currentTargetId) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        } else if (targetType === 'profile') {
            label.textContent = 'Select Profile';
            this.profiles.forEach(profile => {
                const option = document.createElement('option');
                option.value = profile.id;
                option.textContent = profile.name;
                if (currentTargetId && profile.id === currentTargetId) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }
    }

    async updateContentCounts() {
        try {
            const comments = await window.electronAPI.content.getComments();
            const links = await window.electronAPI.content.getLinks();
            const groupLinks = await window.electronAPI.content.getGroupLinks();
            const decoyLinks = await window.electronAPI.content.getDecoyLinks();

            document.getElementById('comments-count').textContent = `${comments.length} comments`;
            document.getElementById('links-count').textContent = `${links.length} links`;
            document.getElementById('group-links-count').textContent = `${groupLinks.length} groups`;
        } catch (error) {
            console.error('Failed to update content counts:', error);
        }
    }

    showCommentsModal() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Manage Comments</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-4 flex justify-between items-center">
                    <div class="space-x-2">
                        <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg" onclick="app.showAddCommentForm()">
                            <i class="fas fa-plus mr-2"></i>Add Comment
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg" onclick="app.showBulkCommentForm()">
                            <i class="fas fa-list mr-2"></i>Bulk Add (One per line)
                        </button>
                    </div>
                    <div class="space-x-2">
                        <button class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg" onclick="app.selectAllComments()">
                            <i class="fas fa-check-double mr-2"></i>Select All
                        </button>
                        <button class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg" onclick="app.saveCommentsSettings()">
                            <i class="fas fa-save mr-2"></i>Save Settings
                        </button>
                    </div>
                </div>
                <div id="comments-list" class="space-y-2 max-h-96 overflow-y-auto">
                    <!-- Comments will be loaded here -->
                </div>
            </div>
        `, { preventClose: true });
        this.loadCommentsList();
    }

    showLinksModal() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Manage Links</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-4 flex justify-between items-center">
                    <div class="space-x-2">
                        <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg" onclick="app.showAddLinkForm()">
                            <i class="fas fa-plus mr-2"></i>Add Link
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg" onclick="app.showBulkLinkForm()">
                            <i class="fas fa-list mr-2"></i>Bulk Add (One per line)
                        </button>
                    </div>
                    <div class="space-x-2">
                        <button class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg" onclick="app.selectAllLinks()">
                            <i class="fas fa-check-double mr-2"></i>Select All
                        </button>
                        <button class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg" onclick="app.saveLinksSettings()">
                            <i class="fas fa-save mr-2"></i>Save Settings
                        </button>
                    </div>
                </div>
                <div id="links-list" class="space-y-2 max-h-96 overflow-y-auto">
                    <!-- Links will be loaded here -->
                </div>
            </div>
        `, { preventClose: true });
        this.loadLinksList();
    }

    showGroupLinksModal() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Manage Group Links</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-4 flex justify-between items-center">
                    <div class="space-x-2">
                        <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg" onclick="app.showAddGroupLinkForm()">
                            <i class="fas fa-plus mr-2"></i>Add Group Link
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg" onclick="app.showBulkGroupLinkForm()">
                            <i class="fas fa-list mr-2"></i>Bulk Add (One per line)
                        </button>
                    </div>
                    <div class="space-x-2">
                        <button class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg" onclick="app.selectAllGroupLinks()">
                            <i class="fas fa-check-double mr-2"></i>Select All
                        </button>
                        <button class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg" onclick="app.saveGroupLinksSettings()">
                            <i class="fas fa-save mr-2"></i>Save Settings
                        </button>
                    </div>
                </div>
                <div id="group-links-list" class="space-y-2 max-h-96 overflow-y-auto">
                    <!-- Group links will be loaded here -->
                </div>
            </div>
        `, { preventClose: true });
        this.loadGroupLinksList();
    }

    showDecoyLinksModal() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Manage Decoy Links</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-4 flex justify-between items-center">
                    <div class="space-x-2">
                        <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg" onclick="app.showAddDecoyLinkForm()">
                            <i class="fas fa-plus mr-2"></i>Add Decoy Link
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg" onclick="app.showBulkDecoyLinkForm()">
                            <i class="fas fa-list mr-2"></i>Bulk Add (One per line)
                        </button>
                    </div>
                    <div class="space-x-2">
                        <button class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg" onclick="app.selectAllDecoyLinks()">
                            <i class="fas fa-check-double mr-2"></i>Select All
                        </button>
                        <button class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg" onclick="app.saveDecoyLinksSettings()">
                            <i class="fas fa-save mr-2"></i>Save Settings
                        </button>
                    </div>
                </div>
                <div id="decoy-links-list" class="space-y-2 max-h-96 overflow-y-auto">
                    <!-- Decoy links will be loaded here -->
                </div>
            </div>
        `, { preventClose: true });
        this.loadDecoyLinksList();
    }

    async saveAutomationSettings() {
        try {
            console.log('💾 Saving automation settings...');

            const settings = {
                like: {
                    enabled: document.getElementById('enable-likes')?.checked || false,
                    delay: parseInt(document.getElementById('like-delay')?.value || '2'),
                    maxPerSession: parseInt(document.getElementById('max-likes')?.value || '50')
                },
                comment: {
                    enabled: document.getElementById('enable-comments')?.checked || false,
                    delay: parseInt(document.getElementById('comment-delay')?.value || '3'),
                    maxPerSession: parseInt(document.getElementById('max-comments')?.value || '20')
                },
                shareTimeline: {
                    enabled: document.getElementById('enable-share-timeline')?.checked || false,
                    delay: parseInt(document.getElementById('share-timeline-delay')?.value || '5'),
                    maxPerSession: parseInt(document.getElementById('max-timeline-shares')?.value || '10')
                },
                shareGroups: {
                    enabled: document.getElementById('enable-share-groups')?.checked || false,
                    delay: parseInt(document.getElementById('share-groups-delay')?.value || '5'),
                    maxPerSession: parseInt(document.getElementById('max-group-shares')?.value || '10')
                }
            };

            console.log('💾 Automation settings to save:', settings);

            this.showLoading('Saving automation settings...');
            const result = await window.electronAPI.settings.saveAutomationSettings(settings);

            console.log('💾 Automation save result:', result);

            if (result.success) {
                this.showToast('Automation settings saved successfully', 'success');

                // Update current settings
                this.currentSettings = this.currentSettings || {};
                this.currentSettings.automation = settings;

                console.log('🔄 Updated current settings:', this.currentSettings);

                // Update Quick Actions buttons immediately and with delay
                this.updateQuickActionsButtons();
                setTimeout(() => {
                    this.updateQuickActionsButtons();
                }, 500);

            } else {
                this.showToast(`Failed to save settings: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('❌ Failed to save automation settings:', error);
            this.showToast(`Error saving settings: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Update Quick Actions buttons based on automation settings
    updateQuickActionsButtons() {
        try {
            console.log('🔄 Updating Quick Actions buttons based on automation settings');

            if (!this.currentSettings?.automation) {
                console.log('⚠️ No automation settings found, skipping button update');
                return;
            }

            const automation = this.currentSettings.automation;
            console.log('🔄 Current automation settings:', automation);

            // Update Like buttons (both dashboard and automation page)
            const likeBtns = document.querySelectorAll('#run-like-btn');
            likeBtns.forEach(likeBtn => {
                if (automation.like?.enabled) {
                    likeBtn.disabled = false;
                    likeBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                    likeBtn.style.pointerEvents = 'auto';
                    likeBtn.style.opacity = '1';
                    console.log('✅ Like button enabled');
                } else {
                    likeBtn.disabled = true;
                    likeBtn.classList.add('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                    likeBtn.style.pointerEvents = 'none';
                    likeBtn.style.opacity = '0.5';
                    console.log('❌ Like button disabled');
                }
            });

            // Update Comment buttons
            const commentBtns = document.querySelectorAll('#run-comment-btn');
            commentBtns.forEach(commentBtn => {
                if (automation.comment?.enabled) {
                    commentBtn.disabled = false;
                    commentBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                    commentBtn.style.pointerEvents = 'auto';
                    commentBtn.style.opacity = '1';
                    console.log('✅ Comment button enabled');
                } else {
                    commentBtn.disabled = true;
                    commentBtn.classList.add('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                    commentBtn.style.pointerEvents = 'none';
                    commentBtn.style.opacity = '0.5';
                    console.log('❌ Comment button disabled');
                }
            });

            // Update Share Timeline buttons
            const shareTimelineBtns = document.querySelectorAll('#run-share-timeline-btn');
            shareTimelineBtns.forEach(shareTimelineBtn => {
                if (automation.shareTimeline?.enabled) {
                    shareTimelineBtn.disabled = false;
                    shareTimelineBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                    shareTimelineBtn.style.pointerEvents = 'auto';
                    shareTimelineBtn.style.opacity = '1';
                    console.log('✅ Share Timeline button enabled');
                } else {
                    shareTimelineBtn.disabled = true;
                    shareTimelineBtn.classList.add('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                    shareTimelineBtn.style.pointerEvents = 'none';
                    shareTimelineBtn.style.opacity = '0.5';
                    console.log('❌ Share Timeline button disabled');
                }
            });

            // Update Share Groups buttons
            const shareGroupsBtns = document.querySelectorAll('#run-share-groups-btn');
            shareGroupsBtns.forEach(shareGroupsBtn => {
                if (automation.shareGroups?.enabled) {
                    shareGroupsBtn.disabled = false;
                    shareGroupsBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                    shareGroupsBtn.style.pointerEvents = 'auto';
                    shareGroupsBtn.style.opacity = '1';
                    console.log('✅ Share Groups button enabled');
                } else {
                    shareGroupsBtn.disabled = true;
                    shareGroupsBtn.classList.add('opacity-50', 'cursor-not-allowed', 'pointer-events-none');
                    shareGroupsBtn.style.pointerEvents = 'none';
                    shareGroupsBtn.style.opacity = '0.5';
                    console.log('❌ Share Groups button disabled');
                }
            });

            console.log('✅ Quick Actions buttons updated successfully');
        } catch (error) {
            console.error('❌ Failed to update Quick Actions buttons:', error);
        }
    }

    async saveBrowserSettings() {
        try {
            const settings = {
                engine: document.getElementById('browser-engine').value,
                headless: document.getElementById('headless-mode').value === 'true',
                userAgent: document.getElementById('user-agent').value,
                pageTimeout: parseInt(document.getElementById('page-timeout').value) * 1000
            };

            this.showLoading('Saving browser settings...');
            const result = await window.electronAPI.settings.saveBrowserSettings(settings);

            if (result.success) {
                this.showToast('Browser settings saved successfully', 'success');
            } else {
                this.showToast(`Failed to save settings: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error saving settings: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Save all settings at once
    async saveAllSettings() {
        try {
            this.showLoading('Saving all settings...');
            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            // Save automation settings
            try {
                const automationSettings = {
                    like: {
                        enabled: document.getElementById('enable-likes')?.checked || false,
                        delay: parseInt(document.getElementById('like-delay')?.value || '2'),
                        maxPerSession: parseInt(document.getElementById('max-likes')?.value || '50')
                    },
                    comment: {
                        enabled: document.getElementById('enable-comments')?.checked || false,
                        delay: parseInt(document.getElementById('comment-delay')?.value || '3'),
                        maxPerSession: parseInt(document.getElementById('max-comments')?.value || '20')
                    },
                    shareTimeline: {
                        enabled: document.getElementById('enable-share-timeline')?.checked || false,
                        delay: parseInt(document.getElementById('share-timeline-delay')?.value || '5'),
                        maxPerSession: parseInt(document.getElementById('max-timeline-shares')?.value || '10')
                    },
                    shareGroups: {
                        enabled: document.getElementById('enable-share-groups')?.checked || false,
                        delay: parseInt(document.getElementById('share-groups-delay')?.value || '5'),
                        maxPerSession: parseInt(document.getElementById('max-group-shares')?.value || '10')
                    }
                };

                const automationResult = await window.electronAPI.settings.saveAutomationSettings(automationSettings);
                if (automationResult.success) {
                    successCount++;
                } else {
                    errorCount++;
                    errors.push('Automation settings: ' + automationResult.error);
                }
            } catch (error) {
                errorCount++;
                errors.push('Automation settings: ' + error.message);
            }

            // Save browser settings
            try {
                const browserSettings = {
                    engine: document.getElementById('browser-engine')?.value || 'chromium',
                    headless: document.getElementById('headless-mode')?.value === 'true',
                    userAgent: document.getElementById('user-agent')?.value || 'default',
                    pageTimeout: parseInt(document.getElementById('page-timeout')?.value || '30') * 1000
                };

                const browserResult = await window.electronAPI.settings.saveBrowserSettings(browserSettings);
                if (browserResult.success) {
                    successCount++;
                } else {
                    errorCount++;
                    errors.push('Browser settings: ' + browserResult.error);
                }
            } catch (error) {
                errorCount++;
                errors.push('Browser settings: ' + error.message);
            }

            // Save schedule settings
            try {
                const scheduleSettings = {
                    defaultDelay: parseInt(document.getElementById('default-delay')?.value || '30'),
                    maxConcurrent: parseInt(document.getElementById('max-concurrent')?.value || '3'),
                    workStart: document.getElementById('work-start')?.value || '09:00',
                    workEnd: document.getElementById('work-end')?.value || '17:00',
                    weekendMode: document.getElementById('weekend-mode')?.value || 'normal'
                };

                const scheduleResult = await window.electronAPI.settings.saveScheduleSettings(scheduleSettings);
                if (scheduleResult.success) {
                    successCount++;
                } else {
                    errorCount++;
                    errors.push('Schedule settings: ' + scheduleResult.error);
                }
            } catch (error) {
                errorCount++;
                errors.push('Schedule settings: ' + error.message);
            }

            // Save target automation settings
            try {
                // Get selected radio button value
                const selectedRadio = document.querySelector('input[name="automation-target"]:checked');
                const targetSettings = {
                    targetType: selectedRadio ? selectedRadio.value : 'all',
                    targetId: document.getElementById('target-selection')?.value || null,
                    decoySettings: {
                        count: parseInt(document.getElementById('decoy-count')?.value || '2'),
                        delay: parseInt(document.getElementById('decoy-delay')?.value || '5') * 1000
                    }
                };

                console.log('💾 Saving target automation settings:', targetSettings);
                const targetResult = await window.electronAPI.settings.saveTargetAutomationSettings(targetSettings);
                console.log('💾 Target automation save result:', targetResult);

                if (targetResult.success) {
                    successCount++;
                    // Update current settings in memory
                    this.currentSettings.targetAutomation = targetSettings;
                    console.log('✅ Target automation settings saved and updated in memory');
                } else {
                    errorCount++;
                    errors.push('Target automation settings: ' + targetResult.error);
                }
            } catch (error) {
                errorCount++;
                errors.push('Target automation settings: ' + error.message);
            }

            // Reload current settings after saving
            if (successCount > 0) {
                console.log('🔄 Reloading settings after save...');
                await this.loadCurrentSettings();
                console.log('✅ Settings reloaded after save');
            }

            // Show results
            if (errorCount === 0) {
                this.showToast(`All settings saved successfully! (${successCount} sections)`, 'success');
            } else if (successCount > 0) {
                this.showToast(`Partially saved: ${successCount} successful, ${errorCount} failed`, 'warning');
                console.error('Settings save errors:', errors);
            } else {
                this.showToast(`Failed to save settings: ${errors.join(', ')}`, 'error');
            }

        } catch (error) {
            this.showToast(`Error saving all settings: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // Content Management Functions
    async loadCommentsList() {
        try {
            const comments = await window.electronAPI.content.getComments();
            const listContainer = document.getElementById('comments-list');

            if (comments.length === 0) {
                listContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No comments added yet</p>';
                return;
            }

            listContainer.innerHTML = comments.map((comment, index) => `
                <div class="bg-gray-50 p-3 rounded-lg flex items-start space-x-3">
                    <input type="checkbox" class="comment-checkbox mt-1" data-index="${index}" ${comment.active !== false ? 'checked' : ''}>
                    <div class="flex-1">
                        <p class="text-sm">${comment.text}</p>
                        <p class="text-xs text-gray-500 mt-1">Added: ${new Date(comment.createdAt).toLocaleString()}</p>
                        <span class="text-xs ${comment.active !== false ? 'text-green-600' : 'text-red-600'} font-medium">
                            ${comment.active !== false ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <button onclick="app.deleteComment(${index})" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        } catch (error) {
            console.error('Failed to load comments:', error);
        }
    }

    async loadLinksList() {
        try {
            const links = await window.electronAPI.content.getLinks();
            const listContainer = document.getElementById('links-list');

            if (links.length === 0) {
                listContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No links added yet</p>';
                return;
            }

            listContainer.innerHTML = links.map((link, index) => `
                <div class="bg-gray-50 p-3 rounded-lg flex items-start space-x-3">
                    <input type="checkbox" class="link-checkbox mt-1" data-index="${index}" ${link.active !== false ? 'checked' : ''}>
                    <div class="flex-1">
                        <p class="text-sm font-medium">${link.title || 'Untitled'}</p>
                        <p class="text-xs text-blue-600 break-all">${link.url}</p>
                        <p class="text-xs text-gray-500 mt-1">Added: ${new Date(link.createdAt).toLocaleString()}</p>
                        <span class="text-xs ${link.active !== false ? 'text-green-600' : 'text-red-600'} font-medium">
                            ${link.active !== false ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <button onclick="app.deleteLink(${index})" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        } catch (error) {
            console.error('Failed to load links:', error);
        }
    }

    async loadGroupLinksList() {
        try {
            const groupLinks = await window.electronAPI.content.getGroupLinks();
            const listContainer = document.getElementById('group-links-list');

            if (groupLinks.length === 0) {
                listContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No group links added yet</p>';
                return;
            }

            listContainer.innerHTML = groupLinks.map((group, index) => `
                <div class="bg-gray-50 p-3 rounded-lg flex items-start space-x-3">
                    <input type="checkbox" class="group-link-checkbox mt-1" data-index="${index}" ${group.active !== false ? 'checked' : ''}>
                    <div class="flex-1">
                        <p class="text-sm font-medium">${group.name || 'Unnamed Group'}</p>
                        <p class="text-xs text-blue-600 break-all">${group.url}</p>
                        <p class="text-xs text-gray-500 mt-1">Added: ${new Date(group.createdAt).toLocaleString()}</p>
                        <span class="text-xs ${group.active !== false ? 'text-green-600' : 'text-red-600'} font-medium">
                            ${group.active !== false ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <button onclick="app.deleteGroupLink(${index})" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        } catch (error) {
            console.error('Failed to load group links:', error);
        }
    }

    async loadDecoyLinksList() {
        try {
            const decoyLinks = await window.electronAPI.content.getDecoyLinks();
            const listContainer = document.getElementById('decoy-links-list');

            if (decoyLinks.length === 0) {
                listContainer.innerHTML = '<p class="text-gray-500 text-center py-4">No decoy links added yet</p>';
                return;
            }

            listContainer.innerHTML = decoyLinks.map((link, index) => `
                <div class="bg-gray-50 p-3 rounded-lg flex items-start space-x-3">
                    <input type="checkbox" class="decoy-link-checkbox mt-1" data-index="${index}" ${link.active !== false ? 'checked' : ''}>
                    <div class="flex-1">
                        <p class="text-sm font-medium">${link.title || 'Untitled'}</p>
                        <p class="text-xs text-blue-600 break-all">${link.url}</p>
                        <p class="text-xs text-gray-500 mt-1">Added: ${new Date(link.createdAt).toLocaleString()}</p>
                        <span class="text-xs ${link.active !== false ? 'text-green-600' : 'text-red-600'} font-medium">
                            ${link.active !== false ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                    <button onclick="app.deleteDecoyLink(${index})" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        } catch (error) {
            console.error('Failed to load decoy links:', error);
        }
    }

    showAddCommentForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Add Comment</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.saveComment(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Comment Text</label>
                        <textarea name="comment" rows="4" class="w-full p-3 border rounded-lg"
                                  placeholder="Enter your comment here..." required></textarea>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Comment
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    showBulkCommentForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Bulk Add Comments</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.saveBulkComments(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Comments (One per line)</label>
                        <textarea name="comments" rows="8" class="w-full p-3 border rounded-lg"
                                  placeholder="Enter comments, one per line:&#10;Great post!&#10;Thanks for sharing&#10;Interesting content" required></textarea>
                        <p class="text-xs text-gray-500 mt-1">Each line will be treated as a separate comment</p>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Comments
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    async saveComment(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const commentText = formData.get('comment').trim();

        if (!commentText) {
            this.showToast('Please enter a comment', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.content.addComment(commentText);
            if (result.success) {
                this.showToast('Comment added successfully', 'success');
                this.showCommentsModal(); // Refresh the modal
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to add comment: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding comment: ${error.message}`, 'error');
        }
    }

    async saveBulkComments(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const commentsText = formData.get('comments').trim();

        if (!commentsText) {
            this.showToast('Please enter comments', 'error');
            return;
        }

        const comments = commentsText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        if (comments.length === 0) {
            this.showToast('No valid comments found', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.content.addBulkComments(comments);
            if (result.success) {
                this.showToast(`${comments.length} comments added successfully`, 'success');
                this.showCommentsModal(); // Refresh the modal
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to add comments: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding comments: ${error.message}`, 'error');
        }
    }

    async deleteComment(index) {
        if (confirm('Are you sure you want to delete this comment?')) {
            try {
                const result = await window.electronAPI.content.deleteComment(index);
                if (result.success) {
                    this.showToast('Comment deleted successfully', 'success');
                    this.loadCommentsList(); // Refresh the list
                    await this.updateContentCounts();
                } else {
                    this.showToast(`Failed to delete comment: ${result.error}`, 'error');
                }
            } catch (error) {
                this.showToast(`Error deleting comment: ${error.message}`, 'error');
            }
        }
    }

    async deleteLink(index) {
        if (confirm('Are you sure you want to delete this link?')) {
            try {
                const result = await window.electronAPI.content.deleteLink(index);
                if (result.success) {
                    this.showToast('Link deleted successfully', 'success');
                    this.loadLinksList(); // Refresh the list
                    await this.updateContentCounts();
                } else {
                    this.showToast(`Failed to delete link: ${result.error}`, 'error');
                }
            } catch (error) {
                this.showToast(`Error deleting link: ${error.message}`, 'error');
            }
        }
    }

    async deleteGroupLink(index) {
        if (confirm('Are you sure you want to delete this group link?')) {
            try {
                const result = await window.electronAPI.content.deleteGroupLink(index);
                if (result.success) {
                    this.showToast('Group link deleted successfully', 'success');
                    this.loadGroupLinksList(); // Refresh the list
                    await this.updateContentCounts();
                } else {
                    this.showToast(`Failed to delete group link: ${result.error}`, 'error');
                }
            } catch (error) {
                this.showToast(`Error deleting group link: ${error.message}`, 'error');
            }
        }
    }

    async deleteDecoyLink(index) {
        if (confirm('Are you sure you want to delete this decoy link?')) {
            try {
                const result = await window.electronAPI.content.deleteDecoyLink(index);
                if (result.success) {
                    this.showToast('Decoy link deleted successfully', 'success');
                    this.loadDecoyLinksList(); // Refresh the list
                    await this.updateContentCounts();
                } else {
                    this.showToast(`Failed to delete decoy link: ${result.error}`, 'error');
                }
            } catch (error) {
                this.showToast(`Error deleting decoy link: ${error.message}`, 'error');
            }
        }
    }

    // Content Selection and Settings Functions
    selectAllComments() {
        const checkboxes = document.querySelectorAll('.comment-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
    }

    async saveCommentsSettings() {
        try {
            const checkboxes = document.querySelectorAll('.comment-checkbox');
            const activeComments = [];

            checkboxes.forEach(checkbox => {
                const index = parseInt(checkbox.dataset.index);
                activeComments.push({
                    index: index,
                    active: checkbox.checked
                });
            });

            const result = await window.electronAPI.content.updateCommentsStatus(activeComments);
            if (result.success) {
                this.showToast('Comments settings saved successfully', 'success');
                this.loadCommentsList(); // Refresh the list
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to save settings: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error saving settings: ${error.message}`, 'error');
        }
    }

    selectAllLinks() {
        const checkboxes = document.querySelectorAll('.link-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
    }

    async saveLinksSettings() {
        try {
            const checkboxes = document.querySelectorAll('.link-checkbox');
            const activeLinks = [];

            checkboxes.forEach(checkbox => {
                const index = parseInt(checkbox.dataset.index);
                activeLinks.push({
                    index: index,
                    active: checkbox.checked
                });
            });

            const result = await window.electronAPI.content.updateLinksStatus(activeLinks);
            if (result.success) {
                this.showToast('Links settings saved successfully', 'success');
                this.loadLinksList(); // Refresh the list
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to save settings: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error saving settings: ${error.message}`, 'error');
        }
    }

    selectAllGroupLinks() {
        const checkboxes = document.querySelectorAll('.group-link-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
    }

    async saveGroupLinksSettings() {
        try {
            const checkboxes = document.querySelectorAll('.group-link-checkbox');
            const activeGroupLinks = [];

            checkboxes.forEach(checkbox => {
                const index = parseInt(checkbox.dataset.index);
                activeGroupLinks.push({
                    index: index,
                    active: checkbox.checked
                });
            });

            const result = await window.electronAPI.content.updateGroupLinksStatus(activeGroupLinks);
            if (result.success) {
                this.showToast('Group links settings saved successfully', 'success');
                this.loadGroupLinksList(); // Refresh the list
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to save settings: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error saving settings: ${error.message}`, 'error');
        }
    }

    selectAllDecoyLinks() {
        const checkboxes = document.querySelectorAll('.decoy-link-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });
    }

    async saveDecoyLinksSettings() {
        try {
            const checkboxes = document.querySelectorAll('.decoy-link-checkbox');
            const activeDecoyLinks = [];

            checkboxes.forEach(checkbox => {
                const index = parseInt(checkbox.dataset.index);
                activeDecoyLinks.push({
                    index: index,
                    active: checkbox.checked
                });
            });

            const result = await window.electronAPI.content.updateDecoyLinksStatus(activeDecoyLinks);
            if (result.success) {
                this.showToast('Decoy links settings saved successfully', 'success');
                this.loadDecoyLinksList(); // Refresh the list
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to save settings: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error saving settings: ${error.message}`, 'error');
        }
    }

    // Target Automation Settings
    async saveTargetAutomationSettings() {
        try {
            // Get selected radio button value
            const selectedRadio = document.querySelector('input[name="automation-target"]:checked');
            const targetType = selectedRadio ? selectedRadio.value : 'all';
            const targetId = document.getElementById('target-selection')?.value;
            const decoyCount = parseInt(document.getElementById('decoy-count')?.value || '2');
            const decoyDelay = parseInt(document.getElementById('decoy-delay')?.value || '5');

            const settings = {
                targetType,
                targetId,
                decoySettings: {
                    count: decoyCount,
                    delay: decoyDelay * 1000
                }
            };

            const result = await window.electronAPI.settings.saveTargetAutomationSettings(settings);
            if (result.success) {
                this.showToast('Target automation settings saved successfully', 'success');
            } else {
                this.showToast(`Failed to save settings: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error saving settings: ${error.message}`, 'error');
        }
    }

    // Red List Management
    async showRedListModal() {
        try {
            const redList = await window.electronAPI.redList.getAll();

            this.showModal(`
                <div class="modal-header">
                    <h3 class="text-lg font-semibold">Red List Management</h3>
                    <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="mb-4 flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            ${redList.length} profiles in red list
                        </div>
                        <div class="space-x-2">
                            <button class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg" onclick="app.showAddToRedListForm()">
                                <i class="fas fa-plus mr-2"></i>Add Profile
                            </button>
                            <button class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg" onclick="app.clearRedListFromModal()">
                                <i class="fas fa-trash mr-2"></i>Clear All
                            </button>
                        </div>
                    </div>
                    <div id="red-list-container" class="space-y-2 max-h-96 overflow-y-auto">
                        ${redList.length === 0 ?
                            '<p class="text-gray-500 text-center py-4">No profiles in red list</p>' :
                            redList.map((entry, index) => `
                                <div class="bg-red-50 border border-red-200 p-3 rounded-lg flex justify-between items-start">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium">${this.getProfileName(entry.profileId) || entry.profileId}</p>
                                        <p class="text-xs text-gray-600">Reason: ${entry.reason}</p>
                                        <p class="text-xs text-gray-500">Added: ${new Date(entry.addedAt).toLocaleString()}</p>
                                        <p class="text-xs text-gray-500">Failures: ${entry.failureCount}</p>
                                    </div>
                                    <button onclick="app.removeFromRedList('${entry.profileId}')" class="text-red-500 hover:text-red-700">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            `).join('')
                        }
                    </div>
                </div>
            `, { preventClose: true });
        } catch (error) {
            this.showToast(`Error loading red list: ${error.message}`, 'error');
        }
    }

    getProfileName(profileId) {
        const profile = this.profiles.find(p => p.id === profileId);
        return profile ? profile.name : null;
    }

    async clearRedList() {
        if (confirm('Are you sure you want to clear the entire red list? This action cannot be undone.')) {
            try {
                const result = await window.electronAPI.redList.clear();
                if (result.success) {
                    this.showToast('Red list cleared successfully', 'success');
                    await this.updateRedListCount();
                } else {
                    this.showToast(`Failed to clear red list: ${result.error}`, 'error');
                }
            } catch (error) {
                this.showToast(`Error clearing red list: ${error.message}`, 'error');
            }
        }
    }

    async clearRedListFromModal() {
        if (confirm('Are you sure you want to clear the entire red list? This action cannot be undone.')) {
            try {
                const result = await window.electronAPI.redList.clear();
                if (result.success) {
                    this.showToast('Red list cleared successfully', 'success');
                    await this.updateRedListCount();
                    this.showRedListModal(); // Refresh modal
                } else {
                    this.showToast(`Failed to clear red list: ${result.error}`, 'error');
                }
            } catch (error) {
                this.showToast(`Error clearing red list: ${error.message}`, 'error');
            }
        }
    }

    async removeFromRedList(profileId) {
        if (confirm('Are you sure you want to remove this profile from the red list?')) {
            try {
                const result = await window.electronAPI.redList.remove(profileId);
                if (result.success) {
                    this.showToast('Profile removed from red list', 'success');
                    await this.updateRedListCount();
                    this.showRedListModal(); // Refresh modal
                } else {
                    this.showToast(`Failed to remove profile: ${result.error}`, 'error');
                }
            } catch (error) {
                this.showToast(`Error removing profile: ${error.message}`, 'error');
            }
        }
    }

    showAddToRedListForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Add Profile to Red List</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.addToRedList(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Select Profile</label>
                        <select name="profileId" required class="w-full p-3 border rounded-lg">
                            <option value="">Choose a profile...</option>
                            ${this.profiles.map(profile => `
                                <option value="${profile.id}">${profile.name} (${profile.type})</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Reason</label>
                        <input type="text" name="reason" class="w-full p-3 border rounded-lg"
                               placeholder="e.g., Automation failed, Account suspended, etc." required>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-plus mr-2"></i>Add to Red List
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    async addToRedList(event) {
        event.preventDefault();
        const formData = new FormData(event.target);

        try {
            const profileId = formData.get('profileId');
            const reason = formData.get('reason').trim();

            if (!profileId || !reason) {
                this.showToast('Please fill in all fields', 'error');
                return;
            }

            const result = await window.electronAPI.redList.add(profileId, reason);
            if (result.success) {
                this.showToast('Profile added to red list', 'success');
                this.closeModal();
                await this.updateRedListCount();
            } else {
                this.showToast(`Failed to add profile: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding profile: ${error.message}`, 'error');
        }
    }

    async updateRedListCount() {
        try {
            const redList = await window.electronAPI.redList.getAll();
            const countElement = document.getElementById('red-list-count');
            if (countElement) {
                countElement.textContent = redList.length;
            }
        } catch (error) {
            console.error('Failed to update red list count:', error);
        }
    }

    // Link Management Forms
    showAddLinkForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Add Link</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.saveLink(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Link Title</label>
                        <input type="text" name="title" class="w-full p-3 border rounded-lg"
                               placeholder="Enter link title..." required>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">URL</label>
                        <input type="url" name="url" class="w-full p-3 border rounded-lg"
                               placeholder="https://example.com" required>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Link
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    showBulkLinkForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Bulk Add Links</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.saveBulkLinks(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Links (One per line)</label>
                        <textarea name="links" rows="8" class="w-full p-3 border rounded-lg"
                                  placeholder="Format: Title|URL (one per line)&#10;Example:&#10;Facebook|https://facebook.com&#10;Google|https://google.com" required></textarea>
                        <p class="text-xs text-gray-500 mt-1">Format: Title|URL (one per line)</p>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Links
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    showAddGroupLinkForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Add Group Link</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.saveGroupLink(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Group Name</label>
                        <input type="text" name="name" class="w-full p-3 border rounded-lg"
                               placeholder="Enter group name..." required>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Group URL</label>
                        <input type="url" name="url" class="w-full p-3 border rounded-lg"
                               placeholder="https://facebook.com/groups/..." required>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Group
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    showBulkGroupLinkForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Bulk Add Group Links</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.saveBulkGroupLinks(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Group Links (One per line)</label>
                        <textarea name="groups" rows="8" class="w-full p-3 border rounded-lg"
                                  placeholder="Format: GroupName|URL (one per line)&#10;Example:&#10;Tech Group|https://facebook.com/groups/tech&#10;Marketing Group|https://facebook.com/groups/marketing" required></textarea>
                        <p class="text-xs text-gray-500 mt-1">Format: GroupName|URL (one per line)</p>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Groups
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    showAddDecoyLinkForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Add Decoy Link</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.saveDecoyLink(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Link Title</label>
                        <input type="text" name="title" class="w-full p-3 border rounded-lg"
                               placeholder="Enter decoy link title..." required>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">URL</label>
                        <input type="url" name="url" class="w-full p-3 border rounded-lg"
                               placeholder="https://example.com" required>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Decoy Link
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    showBulkDecoyLinkForm() {
        this.showModal(`
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Bulk Add Decoy Links</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.saveBulkDecoyLinks(event)">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Decoy Links (One per line)</label>
                        <textarea name="decoyLinks" rows="8" class="w-full p-3 border rounded-lg"
                                  placeholder="Format: Title|URL (one per line)&#10;Example:&#10;News Site|https://news.com&#10;Social Media|https://twitter.com" required></textarea>
                        <p class="text-xs text-gray-500 mt-1">Format: Title|URL (one per line)</p>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button type="button" onclick="app.closeModal()"
                                class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg">
                            Cancel
                        </button>
                        <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg">
                            <i class="fas fa-save mr-2"></i>Save Decoy Links
                        </button>
                    </div>
                </form>
            </div>
        `);
    }

    // Save functions for all content types
    async saveLink(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const linkData = {
            title: formData.get('title').trim(),
            url: formData.get('url').trim()
        };

        if (!linkData.title || !linkData.url) {
            this.showToast('Please fill all fields', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.content.addLink(linkData);
            if (result.success) {
                this.showToast('Link added successfully', 'success');
                this.showLinksModal(); // Refresh the modal
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to add link: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding link: ${error.message}`, 'error');
        }
    }

    async saveBulkLinks(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const linksText = formData.get('links').trim();

        if (!linksText) {
            this.showToast('Please enter links', 'error');
            return;
        }

        const links = linksText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .map(line => {
                const parts = line.split('|');
                if (parts.length >= 2) {
                    return {
                        title: parts[0].trim(),
                        url: parts[1].trim()
                    };
                }
                return null;
            })
            .filter(link => link !== null);

        if (links.length === 0) {
            this.showToast('No valid links found', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.content.addBulkLinks(links);
            if (result.success) {
                this.showToast(`${links.length} links added successfully`, 'success');
                this.showLinksModal(); // Refresh the modal
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to add links: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding links: ${error.message}`, 'error');
        }
    }

    async saveGroupLink(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const groupData = {
            name: formData.get('name').trim(),
            url: formData.get('url').trim()
        };

        if (!groupData.name || !groupData.url) {
            this.showToast('Please fill all fields', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.content.addGroupLink(groupData);
            if (result.success) {
                this.showToast('Group link added successfully', 'success');
                this.showGroupLinksModal(); // Refresh the modal
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to add group link: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding group link: ${error.message}`, 'error');
        }
    }

    async saveBulkGroupLinks(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const groupsText = formData.get('groups').trim();

        if (!groupsText) {
            this.showToast('Please enter group links', 'error');
            return;
        }

        const groups = groupsText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .map(line => {
                const parts = line.split('|');
                if (parts.length >= 2) {
                    return {
                        name: parts[0].trim(),
                        url: parts[1].trim()
                    };
                }
                return null;
            })
            .filter(group => group !== null);

        if (groups.length === 0) {
            this.showToast('No valid group links found', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.content.addBulkGroupLinks(groups);
            if (result.success) {
                this.showToast(`${groups.length} group links added successfully`, 'success');
                this.showGroupLinksModal(); // Refresh the modal
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to add group links: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding group links: ${error.message}`, 'error');
        }
    }

    async saveDecoyLink(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const decoyData = {
            title: formData.get('title').trim(),
            url: formData.get('url').trim()
        };

        if (!decoyData.title || !decoyData.url) {
            this.showToast('Please fill all fields', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.content.addDecoyLink(decoyData);
            if (result.success) {
                this.showToast('Decoy link added successfully', 'success');
                this.showDecoyLinksModal(); // Refresh the modal
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to add decoy link: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding decoy link: ${error.message}`, 'error');
        }
    }

    async saveBulkDecoyLinks(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const decoyLinksText = formData.get('decoyLinks').trim();

        if (!decoyLinksText) {
            this.showToast('Please enter decoy links', 'error');
            return;
        }

        const decoyLinks = decoyLinksText.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .map(line => {
                const parts = line.split('|');
                if (parts.length >= 2) {
                    return {
                        title: parts[0].trim(),
                        url: parts[1].trim()
                    };
                }
                return null;
            })
            .filter(link => link !== null);

        if (decoyLinks.length === 0) {
            this.showToast('No valid decoy links found', 'error');
            return;
        }

        try {
            const result = await window.electronAPI.content.addBulkDecoyLinks(decoyLinks);
            if (result.success) {
                this.showToast(`${decoyLinks.length} decoy links added successfully`, 'success');
                this.showDecoyLinksModal(); // Refresh the modal
                await this.updateContentCounts();
            } else {
                this.showToast(`Failed to add decoy links: ${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast(`Error adding decoy links: ${error.message}`, 'error');
        }
    }



    async getAutomationTargetSettings() {
        try {
            console.log('🔍 [DEBUG] getAutomationTargetSettings called');

            // First try to get from UI if we're on settings page
            const allRadios = document.querySelectorAll('input[name="automation-target"]');
            console.log('🔍 [DEBUG] All radio buttons found:', allRadios.length);

            if (allRadios.length > 0) {
                console.log('🔍 [DEBUG] Radio buttons found in DOM, reading from UI');

                // Get target settings from radio buttons (correct way)
                const selectedRadio = document.querySelector('input[name="automation-target"]:checked');
                console.log('🔍 [DEBUG] Selected radio element:', selectedRadio);
                console.log('🔍 [DEBUG] Selected radio value:', selectedRadio?.value);

                const targetType = selectedRadio ? selectedRadio.value : 'all';

                const targetSelectionElement = document.getElementById('target-selection');
                console.log('🔍 [DEBUG] Target selection element:', targetSelectionElement);
                console.log('🔍 [DEBUG] Target selection value:', targetSelectionElement?.value);

                const targetId = targetSelectionElement?.value || null;

                console.log('🎯 [DEBUG] Getting automation target settings from UI:');
                console.log('🎯 [DEBUG] - Selected radio:', selectedRadio?.value);
                console.log('🎯 [DEBUG] - Target Type:', targetType);
                console.log('🎯 [DEBUG] - Target ID:', targetId);

                // Get decoy settings
                const decoyCountElement = document.getElementById('decoy-count');
                const decoyDelayElement = document.getElementById('decoy-delay');
                console.log('🔍 [DEBUG] Decoy count element:', decoyCountElement?.value);
                console.log('🔍 [DEBUG] Decoy delay element:', decoyDelayElement?.value);

                const decoyCount = parseInt(decoyCountElement?.value || '2');
                const decoyDelay = parseInt(decoyDelayElement?.value || '5');

                // Get decoy links
                const decoyLinks = await window.electronAPI.content.getDecoyLinks();

                const settings = {
                    targetType,
                    targetId,
                    decoySettings: {
                        count: decoyCount,
                        delay: decoyDelay * 1000, // Convert to milliseconds
                        links: decoyLinks
                    }
                };

                console.log('🎯 [DEBUG] Final automation target settings from UI:', settings);
                return settings;
            } else {
                console.log('🔍 [DEBUG] No radio buttons found in DOM, getting from database');

                // Get from database if UI elements not available
                const savedSettings = await window.electronAPI.settings.getTargetAutomationSettings();
                console.log('🔍 [DEBUG] Saved settings from database:', savedSettings);

                // Get decoy links
                const decoyLinks = await window.electronAPI.content.getDecoyLinks();

                const settings = {
                    targetType: savedSettings?.targetType || 'all',
                    targetId: savedSettings?.targetId || null,
                    decoySettings: {
                        count: savedSettings?.decoySettings?.count || 2,
                        delay: savedSettings?.decoySettings?.delay || 5000,
                        links: decoyLinks
                    }
                };

                console.log('🎯 [DEBUG] Final automation target settings from database:', settings);
                return settings;
            }
        } catch (error) {
            console.error('❌ [DEBUG] Failed to get automation target settings:', error);
            const fallbackSettings = {
                targetType: 'all',
                targetId: null,
                decoySettings: {
                    count: 2,
                    delay: 5000,
                    links: []
                }
            };
            console.log('🔄 [DEBUG] Using fallback settings:', fallbackSettings);
            return fallbackSettings;
        }
    }

    // Export/Import Settings Functions
    async exportSettings() {
        try {
            this.showLoading('Exporting settings...');

            // Collect all settings data
            const settingsData = {
                version: '1.0.0',
                exportDate: new Date().toISOString(),
                profiles: await window.electronAPI.profiles.getAll(),
                categories: await window.electronAPI.categories.getAll(),
                comments: await window.electronAPI.content.getComments(),
                links: await window.electronAPI.content.getLinks(),
                groupLinks: await window.electronAPI.content.getGroupLinks(),
                decoyLinks: await window.electronAPI.content.getDecoyLinks(),
                redList: await window.electronAPI.redList.getAll(),
                targetAutomationSettings: await window.electronAPI.settings.getTargetAutomationSettings(),
                automationSettings: await window.electronAPI.settings.getAutomationSettings(),
                browserSettings: await window.electronAPI.settings.getBrowserSettings(),
                scheduleSettings: await window.electronAPI.settings.getScheduleSettings()
            };

            // Select export location
            const exportPath = await window.electronAPI.file.selectSaveFile({
                title: 'Export FaceBot Settings',
                defaultPath: `facebot-settings-${new Date().toISOString().split('T')[0]}.json`,
                filters: [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!exportPath || exportPath.canceled) {
                return;
            }

            // Save settings to file
            const result = await window.electronAPI.file.writeFile(exportPath.filePath, JSON.stringify(settingsData, null, 2));

            if (result.success) {
                this.showToast('Settings exported successfully', 'success');
            } else {
                throw new Error(result.error);
            }

        } catch (error) {
            console.error('Export failed:', error);
            this.showToast(`Export failed: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async importSettings() {
        try {
            // Select settings file
            const settingsFile = await window.electronAPI.file.selectFile({
                title: 'Import FaceBot Settings',
                filters: [
                    { name: 'JSON Files', extensions: ['json'] },
                    { name: 'All Files', extensions: ['*'] }
                ]
            });

            if (!settingsFile || settingsFile.canceled) {
                return;
            }

            // Show import options modal
            this.showImportSettingsModal(settingsFile.filePaths[0]);

        } catch (error) {
            console.error('Import failed:', error);
            this.showToast(`Import failed: ${error.message}`, 'error');
        }
    }

    showImportSettingsModal(filePath) {
        const modal = `
            <div class="modal-header">
                <h3 class="text-lg font-semibold">Import Settings</h3>
                <button onclick="app.closeModal()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form onsubmit="app.executeImportSettings(event)" data-file-path="${filePath}">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Import File</label>
                        <input type="text" value="${filePath}" readonly class="w-full p-2 border rounded-lg bg-gray-100">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">What to Import</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="importProfiles" checked class="mr-2">
                                Profiles
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="importCategories" checked class="mr-2">
                                Categories
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="importComments" checked class="mr-2">
                                Comments
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="importLinks" checked class="mr-2">
                                Links
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="importGroupLinks" checked class="mr-2">
                                Group Links
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="importDecoyLinks" checked class="mr-2">
                                Decoy Links
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="importRedList" class="mr-2">
                                Red List (unchecked by default)
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="importSettings" checked class="mr-2">
                                Application Settings
                            </label>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" name="overwriteExisting" class="mr-2">
                            Overwrite existing data
                        </label>
                        <p class="text-xs text-gray-500 mt-1">If unchecked, will merge with existing data</p>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="app.closeModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <i class="fas fa-download mr-2"></i>Import Settings
                        </button>
                    </div>
                </form>
            </div>
        `;

        this.showModal(modal, { preventClose: true });
    }

    async executeImportSettings(event) {
        event.preventDefault();
        const formData = new FormData(event.target);
        const filePath = event.target.getAttribute('data-file-path');

        try {
            this.closeModal();
            this.showLoading('Importing settings...');

            // Read settings file
            const fileResult = await window.electronAPI.file.readFile(filePath);
            if (!fileResult.success) {
                throw new Error(fileResult.error);
            }

            const settingsData = JSON.parse(fileResult.content);

            // Import options
            const options = {
                importProfiles: formData.get('importProfiles') === 'on',
                importCategories: formData.get('importCategories') === 'on',
                importComments: formData.get('importComments') === 'on',
                importLinks: formData.get('importLinks') === 'on',
                importGroupLinks: formData.get('importGroupLinks') === 'on',
                importDecoyLinks: formData.get('importDecoyLinks') === 'on',
                importRedList: formData.get('importRedList') === 'on',
                importSettings: formData.get('importSettings') === 'on',
                overwriteExisting: formData.get('overwriteExisting') === 'on'
            };

            let importedCount = 0;

            // Import categories first (needed for profiles)
            if (options.importCategories && settingsData.categories) {
                if (options.overwriteExisting) {
                    // Clear existing categories
                    const existingCategories = await window.electronAPI.categories.getAll();
                    for (const cat of existingCategories) {
                        await window.electronAPI.categories.delete(cat.id);
                    }
                }

                for (const category of settingsData.categories) {
                    await window.electronAPI.categories.create(category);
                    importedCount++;
                }
            }

            // Import profiles
            if (options.importProfiles && settingsData.profiles) {
                if (options.overwriteExisting) {
                    // Clear existing profiles
                    const existingProfiles = await window.electronAPI.profiles.getAll();
                    for (const profile of existingProfiles) {
                        await window.electronAPI.profiles.delete(profile.id);
                    }
                }

                for (const profile of settingsData.profiles) {
                    await window.electronAPI.profiles.create(profile);
                    importedCount++;
                }
            }

            // Import content
            if (options.importComments && settingsData.comments) {
                if (options.overwriteExisting) {
                    await window.electronAPI.content.clearComments();
                }
                await window.electronAPI.content.importComments(settingsData.comments);
                importedCount += settingsData.comments.length;
            }

            if (options.importLinks && settingsData.links) {
                if (options.overwriteExisting) {
                    await window.electronAPI.content.clearLinks();
                }
                await window.electronAPI.content.importLinks(settingsData.links);
                importedCount += settingsData.links.length;
            }

            if (options.importGroupLinks && settingsData.groupLinks) {
                if (options.overwriteExisting) {
                    await window.electronAPI.content.clearGroupLinks();
                }
                await window.electronAPI.content.importGroupLinks(settingsData.groupLinks);
                importedCount += settingsData.groupLinks.length;
            }

            if (options.importDecoyLinks && settingsData.decoyLinks) {
                if (options.overwriteExisting) {
                    await window.electronAPI.content.clearDecoyLinks();
                }
                await window.electronAPI.content.importDecoyLinks(settingsData.decoyLinks);
                importedCount += settingsData.decoyLinks.length;
            }

            // Import red list
            if (options.importRedList && settingsData.redList) {
                if (options.overwriteExisting) {
                    await window.electronAPI.redList.clear();
                }
                for (const entry of settingsData.redList) {
                    await window.electronAPI.redList.add(entry.profileId, entry.reason);
                }
                importedCount += settingsData.redList.length;
            }

            // Import settings
            if (options.importSettings) {
                if (settingsData.targetAutomationSettings) {
                    await window.electronAPI.settings.saveTargetAutomationSettings(settingsData.targetAutomationSettings);
                }
                if (settingsData.automationSettings) {
                    await window.electronAPI.settings.saveAutomationSettings(settingsData.automationSettings);
                }
                if (settingsData.browserSettings) {
                    await window.electronAPI.settings.saveBrowserSettings(settingsData.browserSettings);
                }
                if (settingsData.scheduleSettings) {
                    await window.electronAPI.settings.saveScheduleSettings(settingsData.scheduleSettings);
                }
            }

            this.showToast(`Successfully imported ${importedCount} items`, 'success');

            // Reload data
            await this.loadInitialData();
            if (this.currentPage === 'profiles') {
                await this.loadProfiles();
            }
            await this.updateContentCounts();
            await this.updateRedListCount();

        } catch (error) {
            console.error('Import failed:', error);
            this.showToast(`Import failed: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
}

// Initialize the application
const app = new FaceBotApp();
