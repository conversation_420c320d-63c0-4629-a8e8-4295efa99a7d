const { EventEmitter } = require('events');

// Use real UUID - no more mocks
const { v4: uuidv4 } = require('uuid');

const FacebookBot = require('./FacebookBot');
const SmartSelectorManager = require('./SmartSelectorManager');
const FacebookAutomation = require('../automation/FacebookAutomation');

class AutomationController extends EventEmitter {
    constructor(dbManager, profileManager) {
        super();
        this.dbManager = dbManager;
        this.profileManager = profileManager;
        this.activeCampaigns = new Map();
        this.campaignQueues = new Map();

        // Initialize smart selector manager and Facebook automation
        this.smartSelectorManager = new SmartSelectorManager(dbManager);
        this.facebookAutomation = new FacebookAutomation(profileManager, dbManager, this.smartSelectorManager);
    }

    async startCampaign(campaignConfig) {
        try {
            const campaignId = uuidv4();

            // Validate campaign config
            this.validateCampaignConfig(campaignConfig);

            // Get available profiles
            const availableProfiles = await this.profileManager.getAvailableProfiles();

            if (availableProfiles.length === 0) {
                throw new Error('No available profiles for automation');
            }

            // Filter profiles based on campaign selection
            let selectedProfiles = availableProfiles;
            if (campaignConfig.profileIds && campaignConfig.profileIds.length > 0) {
                selectedProfiles = availableProfiles.filter(
                    profile => campaignConfig.profileIds.includes(profile.id)
                );
            }

            if (selectedProfiles.length === 0) {
                throw new Error('No selected profiles are available');
            }

            // Create campaign in database
            const campaign = await this.dbManager.createCampaign({
                name: campaignConfig.name || `Campaign ${Date.now()}`,
                config: campaignConfig,
                totalProfiles: selectedProfiles.length
            });

            // Create campaign execution context
            const campaignContext = {
                id: campaignId,
                dbId: campaign.id,
                config: campaignConfig,
                profiles: selectedProfiles,
                status: 'running',
                startedAt: Date.now(),
                completedProfiles: 0,
                successfulActions: 0,
                failedActions: 0,
                currentBatch: 0,
                totalBatches: Math.ceil(selectedProfiles.length / (campaignConfig.batchSize || 5))
            };

            this.activeCampaigns.set(campaignId, campaignContext);

            // Update campaign status in database
            await this.dbManager.updateCampaign(campaign.id, {
                status: 'running',
                startedAt: Date.now()
            });

            // Start campaign execution
            this.executeCampaign(campaignContext);

            console.log(`Campaign started: ${campaignId} with ${selectedProfiles.length} profiles`);

            // Emit campaign started event
            this.emit('campaignStarted', {
                campaignId,
                profileCount: selectedProfiles.length,
                config: campaignConfig
            });

            return {
                success: true,
                campaignId,
                profileCount: selectedProfiles.length,
                estimatedDuration: this.estimateCampaignDuration(campaignContext)
            };
        } catch (error) {
            console.error('Failed to start campaign:', error);
            throw error;
        }
    }

    async stopCampaign(campaignId) {
        try {
            const campaign = this.activeCampaigns.get(campaignId);
            if (!campaign) {
                throw new Error('Campaign not found or not running');
            }

            campaign.status = 'stopping';

            // Stop any running batches
            if (this.campaignQueues.has(campaignId)) {
                clearTimeout(this.campaignQueues.get(campaignId));
                this.campaignQueues.delete(campaignId);
            }

            // Update database
            await this.dbManager.updateCampaign(campaign.dbId, {
                status: 'stopped',
                completedAt: Date.now(),
                completedProfiles: campaign.completedProfiles,
                successfulActions: campaign.successfulActions,
                failedActions: campaign.failedActions
            });

            // Clean up active profiles
            campaign.profiles.forEach(profile => {
                this.profileManager.markProfileInactive(profile.id);
            });

            this.activeCampaigns.delete(campaignId);

            console.log(`Campaign stopped: ${campaignId}`);

            // Emit campaign stopped event
            this.emit('campaignStopped', { campaignId });

            return { success: true };
        } catch (error) {
            console.error('Failed to stop campaign:', error);
            throw error;
        }
    }

    async getCampaignStatus(campaignId) {
        try {
            const campaign = this.activeCampaigns.get(campaignId);
            if (!campaign) {
                // Check if campaign exists in database
                const dbCampaign = await this.dbManager.getCampaign(campaignId);
                if (dbCampaign) {
                    return {
                        id: campaignId,
                        status: dbCampaign.status,
                        completedProfiles: dbCampaign.completedProfiles,
                        totalProfiles: dbCampaign.totalProfiles,
                        successfulActions: dbCampaign.successfulActions,
                        failedActions: dbCampaign.failedActions,
                        startedAt: dbCampaign.startedAt,
                        completedAt: dbCampaign.completedAt
                    };
                }
                throw new Error('Campaign not found');
            }

            return {
                id: campaignId,
                status: campaign.status,
                completedProfiles: campaign.completedProfiles,
                totalProfiles: campaign.profiles.length,
                successfulActions: campaign.successfulActions,
                failedActions: campaign.failedActions,
                currentBatch: campaign.currentBatch,
                totalBatches: campaign.totalBatches,
                startedAt: campaign.startedAt,
                progress: (campaign.completedProfiles / campaign.profiles.length) * 100
            };
        } catch (error) {
            console.error('Failed to get campaign status:', error);
            throw error;
        }
    }

    async executeCampaign(campaign) {
        try {
            const batchSize = campaign.config.batchSize || 5;
            const batchInterval = (campaign.config.batchInterval || 60) * 1000; // Convert to milliseconds

            // Process profiles in batches
            for (let i = 0; i < campaign.profiles.length; i += batchSize) {
                if (campaign.status !== 'running') {
                    break;
                }

                const batch = campaign.profiles.slice(i, i + batchSize);
                campaign.currentBatch = Math.floor(i / batchSize) + 1;

                console.log(`Processing batch ${campaign.currentBatch}/${campaign.totalBatches} for campaign ${campaign.id}`);

                // Execute batch
                await this.executeBatch(campaign, batch);

                // Wait for batch interval before next batch (except for last batch)
                if (i + batchSize < campaign.profiles.length && campaign.status === 'running') {
                    console.log(`Waiting ${batchInterval / 1000}s before next batch...`);
                    await new Promise(resolve => {
                        const timeoutId = setTimeout(resolve, batchInterval);
                        this.campaignQueues.set(campaign.id, timeoutId);
                    });
                    this.campaignQueues.delete(campaign.id);
                }
            }

            // Campaign completed
            if (campaign.status === 'running') {
                campaign.status = 'completed';

                await this.dbManager.updateCampaign(campaign.dbId, {
                    status: 'completed',
                    completedAt: Date.now(),
                    completedProfiles: campaign.completedProfiles,
                    successfulActions: campaign.successfulActions,
                    failedActions: campaign.failedActions
                });

                console.log(`Campaign completed: ${campaign.id}`);

                // Emit campaign completed event
                this.emit('campaignCompleted', {
                    campaignId: campaign.id,
                    completedProfiles: campaign.completedProfiles,
                    successfulActions: campaign.successfulActions,
                    failedActions: campaign.failedActions
                });
            }

            // Clean up
            this.activeCampaigns.delete(campaign.id);
            campaign.profiles.forEach(profile => {
                this.profileManager.markProfileInactive(profile.id);
            });

        } catch (error) {
            console.error(`Campaign execution failed: ${campaign.id}`, error);

            // Mark campaign as failed
            campaign.status = 'failed';
            await this.dbManager.updateCampaign(campaign.dbId, {
                status: 'failed',
                completedAt: Date.now()
            });

            this.emit('campaignFailed', { campaignId: campaign.id, error: error.message });
        }
    }

    async executeBatch(campaign, profiles) {
        const promises = profiles.map(profile => this.executeProfileActions(campaign, profile));
        await Promise.allSettled(promises);
    }

    async executeProfileActions(campaign, profile) {
        let bot = null;
        try {
            // Mark profile as active
            this.profileManager.markProfileActive(profile.id);

            // Get proxy if assigned
            let proxy = null;
            if (profile.proxyId) {
                proxy = await this.dbManager.getProxy(profile.proxyId);
            }

            // Create and initialize bot
            bot = new FacebookBot(profile, proxy, this.dbManager);
            await bot.initialize();

            // Login
            await bot.login(profile.email, profile.password);

            const config = campaign.config;
            const facebookSettings = profile.facebook || {};

            // Execute actions based on configuration
            if (config.actions.includes('comment') && facebookSettings.settings?.enableComments !== false) {
                await this.executeCommentAction(bot, profile, config, facebookSettings);
            }

            if (config.actions.includes('share') && facebookSettings.settings?.enableShares !== false) {
                await this.executeShareAction(bot, profile, config, facebookSettings);
            }

            if (config.actions.includes('like') && facebookSettings.settings?.enableLikes !== false) {
                await this.executeLikeAction(bot, profile, config);
            }

            if (config.actions.includes('decoy') && facebookSettings.settings?.enableDecoyLinks !== false) {
                await this.executeDecoyAction(bot, profile, config, facebookSettings);
            }

            // Logout
            await bot.logout();

            // Mark profile as used
            const cooldownMinutes = config.cooldownMinutes || 60;
            await this.profileManager.markProfileAsUsed(profile.id, cooldownMinutes);

            campaign.completedProfiles++;

            // Emit progress update
            this.emit('campaignProgress', {
                campaignId: campaign.id,
                completedProfiles: campaign.completedProfiles,
                totalProfiles: campaign.profiles.length,
                currentProfile: profile.name
            });

        } catch (error) {
            console.error(`Profile execution failed: ${profile.name}`, error.message);
            campaign.failedActions++;

            // Log the error
            await this.dbManager.addLog({
                profileId: profile.id,
                action: 'campaign_execution',
                target: campaign.config.postUrl || '',
                status: 'failed',
                message: error.message,
                campaignId: campaign.dbId
            });
        } finally {
            // Clean up
            if (bot) {
                await bot.cleanup();
            }
            this.profileManager.markProfileInactive(profile.id);
        }
    }

    async executeCommentAction(bot, profile, config, facebookSettings) {
        if (!config.postId) return;

        const comments = facebookSettings.comments || [];
        if (comments.length === 0) return;

        const randomComment = comments[Math.floor(Math.random() * comments.length)];
        await bot.createComment(config.postId, randomComment);

        // Add delay
        const delay = (facebookSettings.settings?.delayComment || 4) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));

        const campaign = this.activeCampaigns.get(bot.profile.campaignId);
        if (campaign) campaign.successfulActions++;
    }

    async executeShareAction(bot, profile, config, facebookSettings) {
        if (!config.postUrl) return;

        const shares = facebookSettings.shares || [];
        const shareText = shares.length > 0 ?
            shares[Math.floor(Math.random() * shares.length)] : '';

        await bot.sharePost(config.postUrl, shareText);

        // Add delay
        const delay = (facebookSettings.settings?.delayShare || 7) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));

        const campaign = this.activeCampaigns.get(bot.profile.campaignId);
        if (campaign) campaign.successfulActions++;
    }

    async executeLikeAction(bot, profile, config) {
        if (!config.postUrl) return;

        await bot.performLike(config.postUrl);

        // Add delay
        const delay = 2000 + Math.random() * 3000; // 2-5 seconds
        await new Promise(resolve => setTimeout(resolve, delay));

        const campaign = this.activeCampaigns.get(bot.profile.campaignId);
        if (campaign) campaign.successfulActions++;
    }

    async executeDecoyAction(bot, profile, config, facebookSettings) {
        const decoyLinks = facebookSettings.decoyLinks || [];
        if (decoyLinks.length === 0) return;

        const stayInterval = config.decoyStayInterval || 30000; // 30 seconds default
        await bot.visitDecoyLinks(decoyLinks, stayInterval);

        const campaign = this.activeCampaigns.get(bot.profile.campaignId);
        if (campaign) campaign.successfulActions++;
    }

    validateCampaignConfig(config) {
        if (!config.actions || !Array.isArray(config.actions) || config.actions.length === 0) {
            throw new Error('Campaign must have at least one action');
        }

        const validActions = ['comment', 'share', 'like', 'decoy'];
        const invalidActions = config.actions.filter(action => !validActions.includes(action));
        if (invalidActions.length > 0) {
            throw new Error(`Invalid actions: ${invalidActions.join(', ')}`);
        }

        if (config.actions.includes('comment') && !config.postId) {
            throw new Error('Post ID is required for comment action');
        }

        if ((config.actions.includes('share') || config.actions.includes('like')) && !config.postUrl) {
            throw new Error('Post URL is required for share/like actions');
        }
    }

    estimateCampaignDuration(campaign) {
        const avgActionTime = 30; // seconds per action
        const actionsPerProfile = campaign.config.actions.length;
        const totalTime = campaign.profiles.length * actionsPerProfile * avgActionTime;
        const batchTime = Math.ceil(campaign.profiles.length / (campaign.config.batchSize || 5)) *
                         (campaign.config.batchInterval || 60);

        return totalTime + batchTime; // in seconds
    }

    async stopAllCampaigns() {
        const campaignIds = Array.from(this.activeCampaigns.keys());
        await Promise.all(campaignIds.map(id => this.stopCampaign(id)));
    }

    getActiveCampaigns() {
        return Array.from(this.activeCampaigns.values()).map(campaign => ({
            id: campaign.id,
            status: campaign.status,
            completedProfiles: campaign.completedProfiles,
            totalProfiles: campaign.profiles.length,
            currentBatch: campaign.currentBatch,
            totalBatches: campaign.totalBatches
        }));
    }

    /**
     * Start Facebook group sharing automation
     * @param {object} options - Sharing options
     * @returns {Promise<object>} Automation result
     */
    async startGroupSharing(options) {
        try {
            console.log('🚀 Starting Facebook group sharing automation...');

            const result = await this.facebookAutomation.shareToGroups(options);

            if (result.success) {
                // Emit automation event
                this.emit('automationStarted', {
                    type: 'group_sharing',
                    automationId: result.results.automationId,
                    profileId: options.profileId,
                    totalGroups: options.groups.length
                });
            }

            return result;
        } catch (error) {
            console.error('Failed to start group sharing:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get automation status
     * @param {string} automationId - Automation ID
     * @returns {object|null} Automation status
     */
    getAutomationStatus(automationId) {
        return this.facebookAutomation.getAutomationStatus(automationId);
    }

    /**
     * Stop automation
     * @param {string} automationId - Automation ID
     * @returns {boolean} Success status
     */
    stopAutomation(automationId) {
        return this.facebookAutomation.stopAutomation(automationId);
    }

    /**
     * Get all active automations
     * @returns {Array} Array of active automations
     */
    getActiveAutomations() {
        return this.facebookAutomation.getActiveAutomations();
    }

    /**
     * Get smart selector statistics
     * @returns {object} Selector statistics
     */
    getSmartSelectorStats() {
        return this.smartSelectorManager.getStats();
    }

    /**
     * Run Like automation
     * @param {object} options - Like automation options
     * @returns {Promise<object>} Automation result
     */
    async runLikeAutomation(options = {}) {
        try {
            console.log('🚀 [DEBUG] Starting Like automation...');
            console.log('🚀 [DEBUG] Like automation options received:', options);

            const LikeAutomation = require('../automation/LikeAutomation');
            const likeBot = new LikeAutomation(this.dbManager);

            // Use target settings from options if provided, otherwise get from database
            const targetSettings = options.targetSettings || null;
            console.log('🎯 [DEBUG] Target settings for Like automation:', targetSettings);

            // Get targeted profiles based on automation settings
            const profiles = await this.profileManager.getTargetedProfiles(targetSettings);
            console.log(`📊 [DEBUG] getTargetedProfiles returned ${profiles.length} profiles`);

            if (profiles.length === 0) {
                console.log('❌ [DEBUG] No profiles found for Like automation');
                throw new Error('No available profiles for automation based on target settings');
            }

            console.log(`📋 Found ${profiles.length} targeted profiles for Like automation`);
            console.log('📋 [DEBUG] Profile details:', profiles.map(p => ({ id: p.id, name: p.name })));

            const profile = profiles[0];
            console.log(`🎯 [DEBUG] Using profile for Like automation: ${profile.name} (${profile.id})`);

            // Initialize and run
            console.log('🔄 [DEBUG] Initializing Like automation for profile:', profile.id);
            const initResult = await likeBot.initialize(profile.id);
            if (!initResult.success) {
                console.log('❌ [DEBUG] Like automation initialization failed:', initResult.error);
                throw new Error(initResult.error);
            }

            console.log('✅ [DEBUG] Like automation initialized for profile:', profile.id);
            console.log('🚀 [DEBUG] Starting Like automation...');
            const result = await likeBot.run(options);

            // Cleanup
            console.log('🧹 [DEBUG] Like automation cleanup completed');
            await likeBot.cleanup();

            return result;
        } catch (error) {
            console.error('❌ [DEBUG] Like automation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Run Comment automation
     * @param {object} options - Comment automation options
     * @returns {Promise<object>} Automation result
     */
    async runCommentAutomation(options = {}) {
        try {
            console.log('🚀 Starting Comment automation...');

            const CommentAutomation = require('../automation/CommentAutomation');
            const commentBot = new CommentAutomation(this.dbManager);

            // Get targeted profiles based on automation settings
            const profiles = await this.profileManager.getTargetedProfiles();
            if (profiles.length === 0) {
                throw new Error('No available profiles for automation based on target settings');
            }

            console.log(`📋 Found ${profiles.length} targeted profiles for Comment automation`);
            const profile = profiles[0];

            // Initialize and run
            const initResult = await commentBot.initialize(profile.id);
            if (!initResult.success) {
                throw new Error(initResult.error);
            }

            const result = await commentBot.run(options);

            // Cleanup
            await commentBot.cleanup();

            return result;
        } catch (error) {
            console.error('Comment automation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Run Share Timeline automation
     * @param {object} options - Share Timeline automation options
     * @returns {Promise<object>} Automation result
     */
    async runShareTimelineAutomation(options = {}) {
        try {
            console.log('🚀 Starting Share Timeline automation...');

            const ShareTimelineAutomation = require('../automation/ShareTimelineAutomation');
            const shareBot = new ShareTimelineAutomation(this.dbManager);

            // Get targeted profiles based on automation settings
            const profiles = await this.profileManager.getTargetedProfiles();
            if (profiles.length === 0) {
                throw new Error('No available profiles for automation based on target settings');
            }

            console.log(`📋 Found ${profiles.length} targeted profiles for Share Timeline automation`);
            const profile = profiles[0];

            // Initialize and run
            const initResult = await shareBot.initialize(profile.id);
            if (!initResult.success) {
                throw new Error(initResult.error);
            }

            const result = await shareBot.run(options);

            // Cleanup
            await shareBot.cleanup();

            return result;
        } catch (error) {
            console.error('Share Timeline automation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Run Share Groups automation (using existing FacebookAutomation)
     * @param {object} options - Share Groups automation options
     * @returns {Promise<object>} Automation result
     */
    async runShareGroupsAutomation(options = {}) {
        try {
            console.log('🚀 Starting Share Groups automation...');

            // Use existing Facebook automation for group sharing
            return await this.startGroupSharing(options);
        } catch (error) {
            console.error('Share Groups automation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Cleanup all automations and campaigns
     * @returns {Promise<void>}
     */
    async cleanup() {
        console.log('🧹 Cleaning up automation controller...');

        // Stop all campaigns
        await this.stopAllCampaigns();

        // Cleanup Facebook automation
        await this.facebookAutomation.cleanup();

        console.log('✅ Automation controller cleanup complete');
    }
}

module.exports = AutomationController;
