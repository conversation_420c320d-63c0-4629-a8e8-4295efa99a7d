{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 24417}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 11717}, "server": "https://static.xx.fbcdn.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 26415}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 21138}, "server": "https://www.facebook.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 21138}, "server": "https://facebook.com", "supports_spdy": true}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}