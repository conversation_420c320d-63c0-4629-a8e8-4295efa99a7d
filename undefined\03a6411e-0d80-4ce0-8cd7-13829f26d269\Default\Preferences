{"accessibility": {"captions": {"live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 136}, "autofill": {"last_version_deduped": 136}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 902, "left": 16, "maximized": false, "right": 1398, "top": 114, "work_area_bottom": 720, "work_area_left": 0, "work_area_right": 1366, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "2fb00f98-e21f-4443-a89f-7841364de3d6", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "136.0.7103.25", "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Chromium.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1169\\chrome-win\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1169\\chrome-win\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.519333, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "org.chromium.windows"}, "google": {"services": {"signin_scoped_device_id": "97223ab6-4996-4ff4-bdf0-6529b701cb6a"}}, "history_clusters": {"last_selected_tab": 0}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}}}, "media": {"engagement": {"schema_version": 5}}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.facebook.com:443,*": {"last_modified": "*****************", "setting": {"https://www.facebook.com/": {"next_install_text_animation": {"delay": "***********", "last_shown": "*****************"}}, "https://www.facebook.com/?ref=homescreenpwa": {"couldShowBannerEvents": 1.3393021911931656e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]facebook.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://www.facebook.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 15}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://history/,*": {"last_modified": "13393025471664386", "setting": {"lastEngagementTime": 1.339302547166438e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "https://www.facebook.com:443,*": {"last_modified": "13393025467543267", "setting": {"lastEngagementTime": 1.3393025467543244e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "136.0.7103.25", "creation_time": "13393021908805632", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13393025471664380", "last_time_password_store_metrics_reported": **********.252792, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chromium", "password_hash_data_list": []}, "protection": {"macs": {"browser": {"show_home_button": "44EC50D3A5543009152BD52EA8ED09FB50F1D3094E0F25B784F1683F26E207B3"}, "default_search_provider_data": {"template_url_data": "0C7CCEFFECFD4D634BA8CC476F0EEAD02F9CC38DA8960018E1A9D079246CF848"}, "enterprise_signin": {"policy_recovery_token": "D3D5BCBDE45BAB2D6FE0379E5964E7303B0426E92825FFC1667F639A2015F47A"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "C4EB2AC161F360902F7F5738CF4C039E396CC8D6181C80E669EC9B6A473FB4E9", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "B4DDF3786437F70D34207FC8BCA940EF73E59C12223129F01AF9D1AF04560D04"}, "ui": {"developer_mode": "856E8A0217BEC2917F34BD8244A9AB1362EBF8C260FED4EC451DB44C414F954F"}}, "google": {"services": {"account_id": "7DB53917A95FA93E366829BF6FCA22E9F4093AEF13FAA444071A39CCC2B1ED9D", "last_signed_in_username": "CE0A8F4E90CB742FCBA27CC63C20E7A0566E49549CBDFCA77D9AB83DD1255A88", "last_username": "8FD4F7947ED88460D6A76CB4FD148DC4A236B82A3F06110BFBF45054E3C37102"}}, "homepage": "66E0E9988AD07456A125B11118BB6C6D1BA5D4CB8AE5FD0A53AE83858FA2C836", "homepage_is_newtabpage": "70EEEE97C4E8ED54938267B174147A0561EE662A05B89B159A47C774395AA28A", "media": {"cdm": {"origin_data": "2F9CF1C8760036EA40FA56EBE0CBD1255FB22EA970C247600ED736A09E931CB0"}, "storage_id_salt": "B1985AC16D76A576D919274CAD373BFDBB4470D716EAA7A8DBCB82CCF370873B"}, "pinned_tabs": "3BCB863FD4589E53969B399C1843F13C854FE474DEEEF7DE93A8ECC1FE7C1938", "prefs": {"preference_reset_time": "60500FBC6F2C99B614883279C47907FC5F6ECEFF0A5C0F91ECF810A8617F1066"}, "safebrowsing": {"incidents_sent": "5913EE49952359CF9CB0CD5B7CCE557D16D38F52DCDD862A6DDF7B752A281D80"}, "search_provider_overrides": "419A9786B89A568BCCA207B9375D4989E1EB184F5FF79DDA97BC6C41BE1946A2", "session": {"restore_on_startup": "998C5F94A8259AE1CABAA92CA01392A86044ACB2689491D625762C51B8F37BC2", "startup_urls": "8E77ACC912FE4491CC853ED3DF47E9FB7B97D8BBA04A84CEA1F21FA2DCA7AA43"}}}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13393021908", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQofK4zZ6c5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEJjzuM2enOUX", "last_db_compaction_time": "13392863999000000", "uma_in_sql_start_time": "13393021908865182"}, "sessions": {"event_log": [{"crashed": false, "time": "13393023942605999", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393023944981626", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393024598617387", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393024602638610", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393024616934777", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393024619753241", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393024747171056", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393024750824548", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393024775575628", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393024780993259", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393024840389995", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393024860815626", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393025102574271", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393025106911483", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393025227240868", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13393025257324168", "type": 2, "window_count": 1}, {"crashed": false, "time": "13393025337139055", "type": 0}, {"crashed": true, "time": "13393025428285616", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 3, "time": "13393025477631567", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": false, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "136"}}