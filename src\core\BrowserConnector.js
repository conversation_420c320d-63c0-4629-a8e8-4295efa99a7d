/**
 * BrowserConnector - Interface for browser automation
 * Provides methods to interact with browser instances managed by BrowserWindowManager
 */

class BrowserConnector {
    constructor() {
        this.browserWindowManager = null;
    }

    /**
     * Initialize with BrowserWindowManager instance
     * @param {BrowserWindowManager} browserWindowManager
     */
    initialize(browserWindowManager) {
        this.browserWindowManager = browserWindowManager;
    }

    /**
     * Navigate to URL
     * @param {string} profileId - Profile ID
     * @param {string} url - URL to navigate to
     * @returns {Promise<object>} Navigation result
     */
    async navigateToUrl(profileId, url) {
        try {
            if (!this.browserWindowManager) {
                throw new Error('BrowserConnector not initialized with BrowserWindowManager');
            }

            // Find active window for profile
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const window = activeWindows.find(w => w.profileId === profileId);

            if (!window) {
                throw new Error(`No active browser window found for profile: ${profileId}`);
            }

            // Navigate using BrowserWindowManager
            await this.browserWindowManager.navigateWindow(window.windowId, url);

            return { success: true };
        } catch (error) {
            console.error('❌ Navigation failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Click element using selector
     * @param {string} profileId - Profile ID
     * @param {string} selector - CSS selector
     * @returns {Promise<object>} Click result
     */
    async clickElement(profileId, selector) {
        try {
            if (!this.browserWindowManager) {
                throw new Error('BrowserConnector not initialized with BrowserWindowManager');
            }

            // Find active window for profile
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const window = activeWindows.find(w => w.profileId === profileId);

            if (!window) {
                throw new Error(`No active browser window found for profile: ${profileId}`);
            }

            // Get browser window data
            const windowData = this.browserWindowManager.getWindowContext(window.windowId);
            if (!windowData || !windowData.page) {
                throw new Error('Browser context or page not available');
            }

            // Try to click element
            const element = await windowData.page.$(selector);
            if (element) {
                await element.click();
                return { success: true, result: { found: true } };
            } else {
                return { success: false, result: { found: false }, error: 'Element not found' };
            }
        } catch (error) {
            console.error('❌ Click element failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Type text into element
     * @param {string} profileId - Profile ID
     * @param {string} selector - CSS selector
     * @param {string} text - Text to type
     * @returns {Promise<object>} Type result
     */
    async typeText(profileId, selector, text) {
        try {
            if (!this.browserWindowManager) {
                throw new Error('BrowserConnector not initialized with BrowserWindowManager');
            }

            // Find active window for profile
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const window = activeWindows.find(w => w.profileId === profileId);

            if (!window) {
                throw new Error(`No active browser window found for profile: ${profileId}`);
            }

            // Get browser window data
            const windowData = this.browserWindowManager.getWindowContext(window.windowId);
            if (!windowData || !windowData.page) {
                throw new Error('Browser context or page not available');
            }

            // Try to type text
            const element = await windowData.page.$(selector);
            if (element) {
                await element.fill(text);
                return { success: true };
            } else {
                return { success: false, error: 'Element not found' };
            }
        } catch (error) {
            console.error('❌ Type text failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Wait for element to appear
     * @param {string} profileId - Profile ID
     * @param {string} selector - CSS selector
     * @param {number} timeout - Timeout in milliseconds
     * @returns {Promise<object>} Wait result
     */
    async waitForElement(profileId, selector, timeout = 5000) {
        try {
            if (!this.browserWindowManager) {
                throw new Error('BrowserConnector not initialized with BrowserWindowManager');
            }

            // Find active window for profile
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const window = activeWindows.find(w => w.profileId === profileId);

            if (!window) {
                throw new Error(`No active browser window found for profile: ${profileId}`);
            }

            // Get browser window data
            const windowData = this.browserWindowManager.getWindowContext(window.windowId);
            if (!windowData || !windowData.page) {
                throw new Error('Browser context or page not available');
            }

            // Wait for element
            await windowData.page.waitForSelector(selector, { timeout });
            return { success: true, result: { found: true } };
        } catch (error) {
            console.error('❌ Wait for element failed:', error);
            return { success: false, result: { found: false }, error: error.message };
        }
    }

    /**
     * Execute JavaScript in browser
     * @param {string} profileId - Profile ID
     * @param {string} script - JavaScript code to execute
     * @returns {Promise<object>} Execution result
     */
    async executeScript(profileId, script) {
        try {
            if (!this.browserWindowManager) {
                throw new Error('BrowserConnector not initialized with BrowserWindowManager');
            }

            // Find active window for profile
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const window = activeWindows.find(w => w.profileId === profileId);

            if (!window) {
                throw new Error(`No active browser window found for profile: ${profileId}`);
            }

            // Get browser window data
            const windowData = this.browserWindowManager.getWindowContext(window.windowId);
            if (!windowData || !windowData.page) {
                throw new Error('Browser context or page not available');
            }

            // Execute script
            const result = await windowData.page.evaluate(script);
            return { success: true, result };
        } catch (error) {
            console.error('❌ Execute script failed:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Press a key in the browser
     * @param {string} profileId - Profile ID
     * @param {string} key - Key to press (e.g., 'Enter', 'Tab', 'Escape')
     * @returns {Promise<object>} Press result
     */
    async pressKey(profileId, key) {
        try {
            // Find active window for profile
            const activeWindows = this.browserWindowManager.getActiveWindows();
            const window = activeWindows.find(w => w.profileId === profileId);

            if (!window) {
                throw new Error(`No active browser window found for profile: ${profileId}`);
            }

            // Get browser window data
            const windowData = this.browserWindowManager.getWindowContext(window.windowId);
            if (!windowData || !windowData.page) {
                throw new Error('Browser context or page not available');
            }

            // Press key
            await windowData.page.keyboard.press(key);
            return { success: true };
        } catch (error) {
            console.error(`❌ Press key failed:`, error);
            return { success: false, error: error.message };
        }
    }
}

module.exports = BrowserConnector;
